/********************************************************************************
** Form generated from reading UI file 'main.ui'
**
** Created by: Qt User Interface Compiler version 6.9.1
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAIN_H
#define UI_MAIN_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *styleSheet;
    QHBoxLayout *horizontalLayout_12;
    QFrame *bgApp;
    QHBoxLayout *appLayout;
    QFrame *leftMenuBg;
    QVBoxLayout *verticalLayout_3;
    QFrame *topLogoInfo;
    QFrame *topLogo;
    QLabel *titleLeftApp;
    QLabel *titleLeftDescription;
    QFrame *leftMenuFrame;
    QVBoxLayout *verticalMenuLayout;
    QFrame *toggleBox;
    QVBoxLayout *verticalLayout_4;
    QPushButton *toggleButton;
    QFrame *topMenu;
    QVBoxLayout *verticalLayout_8;
    QPushButton *btn_home;
    QPushButton *btn_widgets;
    QPushButton *btn_ota;
    QPushButton *btn_document;
    QPushButton *btn_exit;
    QFrame *bottomMenu;
    QVBoxLayout *verticalLayout_9;
    QPushButton *toggleLeftBox;
    QFrame *extraLeftBox;
    QVBoxLayout *extraColumLayout;
    QFrame *extraTopBg;
    QVBoxLayout *verticalLayout_5;
    QGridLayout *extraTopLayout;
    QFrame *extraIcon;
    QLabel *extraLabel;
    QPushButton *extraCloseColumnBtn;
    QFrame *extraContent;
    QVBoxLayout *verticalLayout_12;
    QFrame *extraTopMenu;
    QVBoxLayout *verticalLayout_11;
    QPushButton *btn_share;
    QPushButton *btn_adjustments;
    QPushButton *btn_more;
    QFrame *extraCenter;
    QVBoxLayout *verticalLayout_10;
    QTextEdit *textEdit;
    QFrame *extraBottom;
    QFrame *contentBox;
    QVBoxLayout *verticalLayout_2;
    QFrame *contentTopBg;
    QHBoxLayout *horizontalLayout;
    QFrame *leftBox;
    QHBoxLayout *horizontalLayout_3;
    QLabel *titleRightInfo;
    QFrame *rightButtons;
    QHBoxLayout *horizontalLayout_2;
    QPushButton *settingsTopBtn;
    QPushButton *minimizeAppBtn;
    QPushButton *maximizeRestoreAppBtn;
    QPushButton *closeAppBtn;
    QFrame *contentBottom;
    QVBoxLayout *verticalLayout_6;
    QFrame *content;
    QHBoxLayout *horizontalLayout_4;
    QFrame *pagesContainer;
    QVBoxLayout *verticalLayout_15;
    QStackedWidget *stackedWidget;
    QWidget *home;
    QHBoxLayout *horizontalLayout_6;
    QTextEdit *textEdit_2;
    QFrame *frame_6;
    QVBoxLayout *verticalLayout_21;
    QFrame *frame_7;
    QVBoxLayout *verticalLayout_22;
    QHBoxLayout *horizontalLayout_8;
    QSpacerItem *horizontalSpacer_5;
    QFrame *frame_9;
    QHBoxLayout *horizontalLayout_7;
    QLabel *label_15;
    QLabel *info_text;
    QSpacerItem *horizontalSpacer_6;
    QHBoxLayout *horizontalLayout_16;
    QVBoxLayout *verticalLayout_23;
    QHBoxLayout *horizontalLayout_10;
    QLabel *label_10;
    QLineEdit *line_filename;
    QHBoxLayout *horizontalLayout_15;
    QLabel *label_11;
    QLineEdit *line_filepath;
    QHBoxLayout *horizontalLayout_17;
    QLabel *label_12;
    QLineEdit *line_data1;
    QHBoxLayout *horizontalLayout_18;
    QLabel *label_13;
    QLineEdit *line_data2;
    QHBoxLayout *horizontalLayout_19;
    QPushButton *btn_get;
    QPushButton *btn_get_all_data;
    QFrame *frame_8;
    QHBoxLayout *horizontalLayout_21;
    QVBoxLayout *verticalLayout_24;
    QHBoxLayout *horizontalLayout_20;
    QSpacerItem *horizontalSpacer_9;
    QFrame *frame_10;
    QLabel *label_16;
    QSpacerItem *horizontalSpacer_10;
    QHBoxLayout *horizontalLayout_23;
    QPushButton *btn_chang_jump;
    QPushButton *btn_bat_jump;
    QPushButton *btn_time;
    QHBoxLayout *horizontalLayout_24;
    QPushButton *btn_error;
    QPushButton *btn_status;
    QPushButton *btn_dazhaun;
    QHBoxLayout *horizontalLayout_25;
    QPushButton *start_vol_cur;
    QPushButton *pushButton_8;
    QPushButton *pushButton_10;
    QWidget *widgets;
    QVBoxLayout *verticalLayout;
    QFrame *row_1;
    QVBoxLayout *verticalLayout_19;
    QFrame *frame_content_wid_1;
    QHBoxLayout *horizontalLayout_9;
    QLineEdit *lineEdit;
    QPushButton *btn_open_csv;
    QComboBox *btn_combox_image;
    QComboBox *btn_combox_image1;
    QPushButton *btn_create_image;
    QPushButton *btn_clear_image;
    QFrame *frame;
    QVBoxLayout *verticalLayout_16;
    QWidget *widget_2;
    QWidget *ota;
    QHBoxLayout *horizontalLayout_27;
    QFrame *frame_12;
    QHBoxLayout *horizontalLayout_29;
    QVBoxLayout *verticalLayout_34;
    QHBoxLayout *horizontalLayout_22;
    QLabel *label_8;
    QSpacerItem *horizontalSpacer_3;
    QCheckBox *hexShowing_checkBox;
    QPushButton *ClearButton;
    QTextEdit *textEdit_Recive;
    QFrame *frame_2;
    QVBoxLayout *verticalLayout_30;
    QHBoxLayout *horizontalLayout_26;
    QFrame *frame_5;
    QHBoxLayout *horizontalLayout_13;
    QVBoxLayout *verticalLayout_17;
    QLabel *Com_Refresh_Label;
    QLabel *Com_Baud_Label;
    QLabel *Com_Name_Label;
    QLabel *Com_Open_Label;
    QLabel *Com_Close_Label;
    QVBoxLayout *verticalLayout_20;
    QPushButton *Com_Refresh_Button;
    QComboBox *Com_Baud_Combo;
    QComboBox *Com_Name_Combo;
    QPushButton *Com_Open_Button;
    QPushButton *Com_Close_Button;
    QFrame *frame_11;
    QVBoxLayout *verticalLayout_33;
    QVBoxLayout *verticalLayout_28;
    QHBoxLayout *horizontalLayout_14;
    QVBoxLayout *verticalLayout_18;
    QLabel *label_9;
    QLabel *label_14;
    QLabel *label_17;
    QVBoxLayout *verticalLayout_26;
    QPushButton *pushButton_2;
    QComboBox *comboBox;
    QPushButton *Com_Open_file;
    QVBoxLayout *verticalLayout_27;
    QLabel *Com_isOpenOrNot_Label;
    QLineEdit *Com_file_line;
    QProgressBar *progressBar;
    QVBoxLayout *verticalLayout_31;
    QFrame *frame_4;
    QVBoxLayout *verticalLayout_29;
    QComboBox *Command_select;
    QLineEdit *lineEdit_3;
    QSpacerItem *verticalSpacer_2;
    QFrame *frame_3;
    QVBoxLayout *verticalLayout_32;
    QHBoxLayout *horizontalLayout_11;
    QLabel *label_7;
    QSpacerItem *horizontalSpacer;
    QCheckBox *hexSending_checkBox;
    QPushButton *Send_Button;
    QTextEdit *textEdit_Send;
    QWidget *Real_time_charts;
    QVBoxLayout *verticalLayout_36;
    QFrame *frame_13;
    QVBoxLayout *verticalLayout_35;
    QVBoxLayout *verticalLayout_25;
    QHBoxLayout *horizontalLayout_28;
    QLineEdit *Chart_line;
    QPushButton *Chart_btn;
    QWidget *Chart_widget;
    QFrame *extraRightBox;
    QVBoxLayout *verticalLayout_7;
    QFrame *contentSettings;
    QVBoxLayout *verticalLayout_13;
    QFrame *topMenus;
    QVBoxLayout *verticalLayout_14;
    QPushButton *btn_message;
    QPushButton *btn_print;
    QPushButton *btn_logout;
    QFrame *bottomBar;
    QHBoxLayout *horizontalLayout_5;
    QLabel *creditsLabel;
    QLabel *version;
    QFrame *frame_size_grip;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName("MainWindow");
        MainWindow->resize(1280, 833);
        MainWindow->setMinimumSize(QSize(940, 560));
        styleSheet = new QWidget(MainWindow);
        styleSheet->setObjectName("styleSheet");
        QFont font;
        font.setFamilies({QString::fromUtf8("\345\276\256\350\275\257\351\233\205\351\273\221")});
        font.setPointSize(11);
        font.setBold(false);
        font.setItalic(false);
        styleSheet->setFont(font);
        styleSheet->setStyleSheet(QString::fromUtf8("/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"\n"
"SET APP STYLESHEET - FULL STYLES HERE\n"
"DARK THEME - DRACULA COLOR BASED\n"
"\n"
"///////////////////////////////////////////////////////////////////////////////////////////////// */\n"
"\n"
"QWidget{\n"
"	color: rgb(221, 221, 221);\n"
"	font: 11pt \"\345\276\256\350\275\257\351\233\205\351\273\221\";\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"Tooltip */\n"
"QToolTip {\n"
"	color: #ffffff;\n"
"	background-color: rgba(33, 37, 43, 180);\n"
"	border: 1px solid rgb(44, 49, 58);\n"
"	background-image: none;\n"
"	background-position: left center;\n"
"    background-repeat: no-repeat;\n"
"	border: none;\n"
"	border-left: 2px solid rgb(255, 121, 198);\n"
"	text-align: left;\n"
"	padding-left: 8px;\n"
"	margin: 0px;\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
""
                        "Bg App */\n"
"#bgApp {	\n"
"	background-color: rgb(40, 44, 52);\n"
"	border: 1px solid rgb(44, 49, 58);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"Left Menu */\n"
"#leftMenuBg {	\n"
"	background-color: rgb(33, 37, 43);\n"
"}\n"
"#topLogo {\n"
"	background-color: rgb(33, 37, 43);\n"
"	background-image: url(:/images/images/images/PyDracula.png);\n"
"	background-position: centered;\n"
"	background-repeat: no-repeat;\n"
"}\n"
"#titleLeftApp { font: 63 12pt \"Segoe UI Semibold\"; }\n"
"#titleLeftDescription { font: 8pt \"Segoe UI\"; color: rgb(189, 147, 249); }\n"
"\n"
"/* MENUS */\n"
"#topMenu .QPushButton {	\n"
"	background-position: left center;\n"
"    background-repeat: no-repeat;\n"
"	border: none;\n"
"	border-left: 22px solid transparent;\n"
"	background-color: transparent;\n"
"	text-align: left;\n"
"	padding-left: 44px;\n"
"}\n"
"#topMenu .QPushButton:hover {\n"
"	background-color: rgb(40, 44, 52);\n"
"}\n"
"#topMenu .QPushButton:p"
                        "ressed {	\n"
"	background-color: rgb(189, 147, 249);\n"
"	color: rgb(255, 255, 255);\n"
"}\n"
"#bottomMenu .QPushButton {	\n"
"	background-position: left center;\n"
"    background-repeat: no-repeat;\n"
"	border: none;\n"
"	border-left: 20px solid transparent;\n"
"	background-color:transparent;\n"
"	text-align: left;\n"
"	padding-left: 44px;\n"
"}\n"
"#bottomMenu .QPushButton:hover {\n"
"	background-color: rgb(40, 44, 52);\n"
"}\n"
"#bottomMenu .QPushButton:pressed {	\n"
"	background-color: rgb(189, 147, 249);\n"
"	color: rgb(255, 255, 255);\n"
"}\n"
"#leftMenuFrame{\n"
"	border-top: 3px solid rgb(44, 49, 58);\n"
"}\n"
"\n"
"/* Toggle Button */\n"
"#toggleButton {\n"
"	background-position: left center;\n"
"    background-repeat: no-repeat;\n"
"	border: none;\n"
"	border-left: 20px solid transparent;\n"
"	background-color: rgb(37, 41, 48);\n"
"	text-align: left;\n"
"	padding-left: 44px;\n"
"	color: rgb(113, 126, 149);\n"
"}\n"
"#toggleButton:hover {\n"
"	background-color: rgb(40, 44, 52);\n"
"}\n"
"#toggleButto"
                        "n:pressed {\n"
"	background-color: rgb(189, 147, 249);\n"
"}\n"
"\n"
"/* Title Menu */\n"
"#titleRightInfo { padding-left: 10px; }\n"
"\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"Extra Tab */\n"
"#extraLeftBox {	\n"
"	background-color: rgb(44, 49, 58);\n"
"}\n"
"#extraTopBg{	\n"
"	background-color: rgb(189, 147, 249)\n"
"}\n"
"\n"
"/* Icon */\n"
"#extraIcon {\n"
"	background-position: center;\n"
"	background-repeat: no-repeat;\n"
"	background-image: url(:/icons/images/icons/icon_settings.png);\n"
"}\n"
"\n"
"/* Label */\n"
"#extraLabel { color: rgb(255, 255, 255); }\n"
"\n"
"/* Btn Close */\n"
"#extraCloseColumnBtn { background-color: rgba(255, 255, 255, 0); border: none;  border-radius: 5px; }\n"
"#extraCloseColumnBtn:hover { background-color: rgb(196, 161, 249); border-style: solid; border-radius: 4px; }\n"
"#extraCloseColumnBtn:pressed { background-color: rgb(180, 141, 238); border-style: solid; border-radius: 4px; }\n"
"\n"
"/* Extra Co"
                        "ntent */\n"
"#extraContent{\n"
"	border-top: 3px solid rgb(40, 44, 52);\n"
"}\n"
"\n"
"/* Extra Top Menus */\n"
"#extraTopMenu .QPushButton {\n"
"background-position: left center;\n"
"    background-repeat: no-repeat;\n"
"	border: none;\n"
"	border-left: 22px solid transparent;\n"
"	background-color:transparent;\n"
"	text-align: left;\n"
"	padding-left: 44px;\n"
"}\n"
"#extraTopMenu .QPushButton:hover {\n"
"	background-color: rgb(40, 44, 52);\n"
"}\n"
"#extraTopMenu .QPushButton:pressed {	\n"
"	background-color: rgb(189, 147, 249);\n"
"	color: rgb(255, 255, 255);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"Content App */\n"
"#contentTopBg{	\n"
"	background-color: rgb(33, 37, 43);\n"
"}\n"
"#contentBottom{\n"
"	border-top: 3px solid rgb(44, 49, 58);\n"
"}\n"
"\n"
"/* Top Buttons */\n"
"#rightButtons .QPushButton { background-color: rgba(255, 255, 255, 0); border: none;  border-radius: 5px; }\n"
"#rightButtons .QPushButton:hover { backgr"
                        "ound-color: rgb(44, 49, 57); border-style: solid; border-radius: 4px; }\n"
"#rightButtons .QPushButton:pressed { background-color: rgb(23, 26, 30); border-style: solid; border-radius: 4px; }\n"
"\n"
"/* Theme Settings */\n"
"#extraRightBox { background-color: rgb(44, 49, 58); }\n"
"#themeSettingsTopDetail { background-color: rgb(189, 147, 249); }\n"
"\n"
"/* Bottom Bar */\n"
"#bottomBar { background-color: rgb(44, 49, 58); }\n"
"#bottomBar QLabel { font-size: 11px; color: rgb(113, 126, 149); padding-left: 10px; padding-right: 10px; padding-bottom: 2px; }\n"
"\n"
"/* CONTENT SETTINGS */\n"
"/* MENUS */\n"
"#contentSettings .QPushButton {	\n"
"	background-position: left center;\n"
"    background-repeat: no-repeat;\n"
"	border: none;\n"
"	border-left: 22px solid transparent;\n"
"	background-color:transparent;\n"
"	text-align: left;\n"
"	padding-left: 44px;\n"
"}\n"
"#contentSettings .QPushButton:hover {\n"
"	background-color: rgb(40, 44, 52);\n"
"}\n"
"#contentSettings .QPushButton:pressed {	\n"
"	background-col"
                        "or: rgb(189, 147, 249);\n"
"	color: rgb(255, 255, 255);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"QTableWidget */\n"
"QTableWidget {	\n"
"	background-color: transparent;\n"
"	padding: 10px;\n"
"	border-radius: 5px;\n"
"	gridline-color: rgb(44, 49, 58);\n"
"	border-bottom: 1px solid rgb(44, 49, 60);\n"
"}\n"
"QTableWidget::item{\n"
"	border-color: rgb(44, 49, 60);\n"
"	padding-left: 5px;\n"
"	padding-right: 5px;\n"
"	gridline-color: rgb(44, 49, 60);\n"
"}\n"
"QTableWidget::item:selected{\n"
"	background-color: rgb(189, 147, 249);\n"
"}\n"
"QHeaderView::section{\n"
"	background-color: rgb(33, 37, 43);\n"
"	max-width: 30px;\n"
"	border: 1px solid rgb(44, 49, 58);\n"
"	border-style: none;\n"
"    border-bottom: 1px solid rgb(44, 49, 60);\n"
"    border-right: 1px solid rgb(44, 49, 60);\n"
"}\n"
"QTableWidget::horizontalHeader {	\n"
"	background-color: rgb(33, 37, 43);\n"
"}\n"
"QHeaderView::section:horizontal\n"
"{\n"
"    border: 1px so"
                        "lid rgb(33, 37, 43);\n"
"	background-color: rgb(33, 37, 43);\n"
"	padding: 3px;\n"
"	border-top-left-radius: 7px;\n"
"    border-top-right-radius: 7px;\n"
"}\n"
"QHeaderView::section:vertical\n"
"{\n"
"    border: 1px solid rgb(44, 49, 60);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"LineEdit */\n"
"QLineEdit {\n"
"	background-color: rgb(33, 37, 43);\n"
"	border-radius: 5px;\n"
"	border: 2px solid rgb(33, 37, 43);\n"
"	padding-left: 10px;\n"
"	selection-color: rgb(255, 255, 255);\n"
"	selection-background-color: rgb(255, 121, 198);\n"
"}\n"
"QLineEdit:hover {\n"
"	border: 2px solid rgb(64, 71, 88);\n"
"}\n"
"QLineEdit:focus {\n"
"	border: 2px solid rgb(91, 101, 124);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"PlainTextEdit */\n"
"QPlainTextEdit {\n"
"	background-color: rgb(27, 29, 35);\n"
"	border-radius: 5px;\n"
"	padding: 10px;\n"
"	selection-color: rgb(255,"
                        " 255, 255);\n"
"	selection-background-color: rgb(255, 121, 198);\n"
"}\n"
"QPlainTextEdit  QScrollBar:vertical {\n"
"    width: 8px;\n"
" }\n"
"QPlainTextEdit  QScrollBar:horizontal {\n"
"    height: 8px;\n"
" }\n"
"QPlainTextEdit:hover {\n"
"	border: 2px solid rgb(64, 71, 88);\n"
"}\n"
"QPlainTextEdit:focus {\n"
"	border: 2px solid rgb(91, 101, 124);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"ScrollBars */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    background: rgb(52, 59, 72);\n"
"    height: 8px;\n"
"    margin: 0px 21px 0 21px;\n"
"	border-radius: 0px;\n"
"}\n"
"QScrollBar::handle:horizontal {\n"
"    background: rgb(189, 147, 249);\n"
"    min-width: 25px;\n"
"	border-radius: 4px\n"
"}\n"
"QScrollBar::add-line:horizontal {\n"
"    border: none;\n"
"    background: rgb(55, 63, 77);\n"
"    width: 20px;\n"
"	border-top-right-radius: 4px;\n"
"    border-bottom-right-radius: 4px;\n"
"    subcontrol-position: right;\n"
""
                        "    subcontrol-origin: margin;\n"
"}\n"
"QScrollBar::sub-line:horizontal {\n"
"    border: none;\n"
"    background: rgb(55, 63, 77);\n"
"    width: 20px;\n"
"	border-top-left-radius: 4px;\n"
"    border-bottom-left-radius: 4px;\n"
"    subcontrol-position: left;\n"
"    subcontrol-origin: margin;\n"
"}\n"
"QScrollBar::up-arrow:horizontal, QScrollBar::down-arrow:horizontal\n"
"{\n"
"     background: none;\n"
"}\n"
"QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal\n"
"{\n"
"     background: none;\n"
"}\n"
" QScrollBar:vertical {\n"
"	border: none;\n"
"    background: rgb(52, 59, 72);\n"
"    width: 8px;\n"
"    margin: 21px 0 21px 0;\n"
"	border-radius: 0px;\n"
" }\n"
" QScrollBar::handle:vertical {	\n"
"	background: rgb(189, 147, 249);\n"
"    min-height: 25px;\n"
"	border-radius: 4px\n"
" }\n"
" QScrollBar::add-line:vertical {\n"
"     border: none;\n"
"    background: rgb(55, 63, 77);\n"
"     height: 20px;\n"
"	border-bottom-left-radius: 4px;\n"
"    border-bottom-right-radius: 4px;\n"
"    "
                        " subcontrol-position: bottom;\n"
"     subcontrol-origin: margin;\n"
" }\n"
" QScrollBar::sub-line:vertical {\n"
"	border: none;\n"
"    background: rgb(55, 63, 77);\n"
"     height: 20px;\n"
"	border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"     subcontrol-position: top;\n"
"     subcontrol-origin: margin;\n"
" }\n"
" QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {\n"
"     background: none;\n"
" }\n"
"\n"
" QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {\n"
"     background: none;\n"
" }\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"CheckBox */\n"
"QCheckBox::indicator {\n"
"    border: 3px solid rgb(52, 59, 72);\n"
"	width: 15px;\n"
"	height: 15px;\n"
"	border-radius: 10px;\n"
"    background: rgb(44, 49, 60);\n"
"}\n"
"QCheckBox::indicator:hover {\n"
"    border: 3px solid rgb(58, 66, 81);\n"
"}\n"
"QCheckBox::indicator:checked {\n"
"    background: 3px solid rgb(52, 59, 72);\n"
"	bord"
                        "er: 3px solid rgb(52, 59, 72);	\n"
"	background-image: url(:/icons/images/icons/cil-check-alt.png);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"RadioButton */\n"
"QRadioButton::indicator {\n"
"    border: 3px solid rgb(52, 59, 72);\n"
"	width: 15px;\n"
"	height: 15px;\n"
"	border-radius: 10px;\n"
"    background: rgb(44, 49, 60);\n"
"}\n"
"QRadioButton::indicator:hover {\n"
"    border: 3px solid rgb(58, 66, 81);\n"
"}\n"
"QRadioButton::indicator:checked {\n"
"    background: 3px solid rgb(94, 106, 130);\n"
"	border: 3px solid rgb(52, 59, 72);	\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"ComboBox */\n"
"QComboBox{\n"
"	background-color: rgb(27, 29, 35);\n"
"	border-radius: 5px;\n"
"	border: 2px solid rgb(33, 37, 43);\n"
"	padding: 5px;\n"
"	padding-left: 10px;\n"
"}\n"
"QComboBox:hover{\n"
"	border: 2px solid rgb(64, 71, 88);\n"
"}\n"
"QComboBox::drop-down {\n"
""
                        "	subcontrol-origin: padding;\n"
"	subcontrol-position: top right;\n"
"	width: 25px; \n"
"	border-left-width: 3px;\n"
"	border-left-color: rgba(39, 44, 54, 150);\n"
"	border-left-style: solid;\n"
"	border-top-right-radius: 3px;\n"
"	border-bottom-right-radius: 3px;	\n"
"	background-image: url(:/icons/images/icons/cil-arrow-bottom.png);\n"
"	background-position: center;\n"
"	background-repeat: no-reperat;\n"
" }\n"
"QComboBox QAbstractItemView {\n"
"	color: rgb(255, 121, 198);	\n"
"	background-color: rgb(33, 37, 43);\n"
"	padding: 10px;\n"
"	selection-background-color: rgb(39, 44, 54);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"Sliders */\n"
"QSlider::groove:horizontal {\n"
"    border-radius: 5px;\n"
"    height: 10px;\n"
"	margin: 0px;\n"
"	background-color: rgb(52, 59, 72);\n"
"}\n"
"QSlider::groove:horizontal:hover {\n"
"	background-color: rgb(55, 62, 76);\n"
"}\n"
"QSlider::handle:horizontal {\n"
"    background-color: rgb(189, 147"
                        ", 249);\n"
"    border: none;\n"
"    height: 10px;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"QSlider::handle:horizontal:hover {\n"
"    background-color: rgb(195, 155, 255);\n"
"}\n"
"QSlider::handle:horizontal:pressed {\n"
"    background-color: rgb(255, 121, 198);\n"
"}\n"
"\n"
"QSlider::groove:vertical {\n"
"    border-radius: 5px;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	background-color: rgb(52, 59, 72);\n"
"}\n"
"QSlider::groove:vertical:hover {\n"
"	background-color: rgb(55, 62, 76);\n"
"}\n"
"QSlider::handle:vertical {\n"
"    background-color: rgb(189, 147, 249);\n"
"	border: none;\n"
"    height: 10px;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"QSlider::handle:vertical:hover {\n"
"    background-color: rgb(195, 155, 255);\n"
"}\n"
"QSlider::handle:vertical:pressed {\n"
"    background-color: rgb(255, 121, 198);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
""
                        "CommandLinkButton */\n"
"QCommandLinkButton {	\n"
"	color: rgb(255, 121, 198);\n"
"	border-radius: 5px;\n"
"	padding: 5px;\n"
"	color: rgb(255, 170, 255);\n"
"}\n"
"QCommandLinkButton:hover {	\n"
"	color: rgb(255, 170, 255);\n"
"	background-color: rgb(44, 49, 60);\n"
"}\n"
"QCommandLinkButton:pressed {	\n"
"	color: rgb(189, 147, 249);\n"
"	background-color: rgb(52, 58, 71);\n"
"}\n"
"\n"
"/* /////////////////////////////////////////////////////////////////////////////////////////////////\n"
"Button */\n"
"#pagesContainer QPushButton {\n"
"	border: 2px solid rgb(52, 59, 72);\n"
"	border-radius: 5px;	\n"
"	background-color: rgb(52, 59, 72);\n"
"}\n"
"#pagesContainer QPushButton:hover {\n"
"	background-color: rgb(57, 65, 80);\n"
"	border: 2px solid rgb(61, 70, 86);\n"
"}\n"
"#pagesContainer QPushButton:pressed {	\n"
"	background-color: rgb(35, 40, 49);\n"
"	border: 2px solid rgb(43, 50, 61);\n"
"}\n"
"\n"
""));
        horizontalLayout_12 = new QHBoxLayout(styleSheet);
        horizontalLayout_12->setSpacing(0);
        horizontalLayout_12->setObjectName("horizontalLayout_12");
        horizontalLayout_12->setContentsMargins(10, 10, 10, 10);
        bgApp = new QFrame(styleSheet);
        bgApp->setObjectName("bgApp");
        bgApp->setStyleSheet(QString::fromUtf8(""));
        bgApp->setFrameShape(QFrame::NoFrame);
        bgApp->setFrameShadow(QFrame::Raised);
        appLayout = new QHBoxLayout(bgApp);
        appLayout->setSpacing(0);
        appLayout->setObjectName("appLayout");
        appLayout->setContentsMargins(0, 0, 0, 0);
        leftMenuBg = new QFrame(bgApp);
        leftMenuBg->setObjectName("leftMenuBg");
        leftMenuBg->setMinimumSize(QSize(60, 0));
        leftMenuBg->setMaximumSize(QSize(60, ********));
        leftMenuBg->setFrameShape(QFrame::NoFrame);
        leftMenuBg->setFrameShadow(QFrame::Raised);
        verticalLayout_3 = new QVBoxLayout(leftMenuBg);
        verticalLayout_3->setSpacing(0);
        verticalLayout_3->setObjectName("verticalLayout_3");
        verticalLayout_3->setContentsMargins(0, 0, 0, 0);
        topLogoInfo = new QFrame(leftMenuBg);
        topLogoInfo->setObjectName("topLogoInfo");
        topLogoInfo->setMinimumSize(QSize(0, 50));
        topLogoInfo->setMaximumSize(QSize(********, 50));
        topLogoInfo->setFrameShape(QFrame::NoFrame);
        topLogoInfo->setFrameShadow(QFrame::Raised);
        topLogo = new QFrame(topLogoInfo);
        topLogo->setObjectName("topLogo");
        topLogo->setGeometry(QRect(10, 5, 42, 42));
        topLogo->setMinimumSize(QSize(42, 42));
        topLogo->setMaximumSize(QSize(42, 42));
        topLogo->setStyleSheet(QString::fromUtf8("border-image: url(:/images/images/images/ui\345\233\276\346\240\207.png);\n"
"border-radius: 21px;"));
        topLogo->setFrameShape(QFrame::NoFrame);
        topLogo->setFrameShadow(QFrame::Raised);
        titleLeftApp = new QLabel(topLogoInfo);
        titleLeftApp->setObjectName("titleLeftApp");
        titleLeftApp->setGeometry(QRect(70, 8, 160, 20));
        QFont font1;
        font1.setFamilies({QString::fromUtf8("Segoe UI Semibold")});
        font1.setPointSize(12);
        font1.setBold(false);
        font1.setItalic(false);
        titleLeftApp->setFont(font1);
        titleLeftApp->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop);
        titleLeftDescription = new QLabel(topLogoInfo);
        titleLeftDescription->setObjectName("titleLeftDescription");
        titleLeftDescription->setGeometry(QRect(70, 27, 160, 16));
        titleLeftDescription->setMaximumSize(QSize(********, 16));
        QFont font2;
        font2.setFamilies({QString::fromUtf8("Segoe UI")});
        font2.setPointSize(8);
        font2.setBold(false);
        font2.setItalic(false);
        titleLeftDescription->setFont(font2);
        titleLeftDescription->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop);

        verticalLayout_3->addWidget(topLogoInfo);

        leftMenuFrame = new QFrame(leftMenuBg);
        leftMenuFrame->setObjectName("leftMenuFrame");
        leftMenuFrame->setFrameShape(QFrame::NoFrame);
        leftMenuFrame->setFrameShadow(QFrame::Raised);
        verticalMenuLayout = new QVBoxLayout(leftMenuFrame);
        verticalMenuLayout->setSpacing(0);
        verticalMenuLayout->setObjectName("verticalMenuLayout");
        verticalMenuLayout->setContentsMargins(0, 0, 0, 0);
        toggleBox = new QFrame(leftMenuFrame);
        toggleBox->setObjectName("toggleBox");
        toggleBox->setMaximumSize(QSize(********, 45));
        toggleBox->setFrameShape(QFrame::NoFrame);
        toggleBox->setFrameShadow(QFrame::Raised);
        verticalLayout_4 = new QVBoxLayout(toggleBox);
        verticalLayout_4->setSpacing(0);
        verticalLayout_4->setObjectName("verticalLayout_4");
        verticalLayout_4->setContentsMargins(0, 0, 0, 0);
        toggleButton = new QPushButton(toggleBox);
        toggleButton->setObjectName("toggleButton");
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Fixed);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(toggleButton->sizePolicy().hasHeightForWidth());
        toggleButton->setSizePolicy(sizePolicy);
        toggleButton->setMinimumSize(QSize(0, 45));
        toggleButton->setFont(font);
        toggleButton->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        toggleButton->setLayoutDirection(Qt::LeftToRight);
        toggleButton->setStyleSheet(QString::fromUtf8("background-image: url(:/icons/images/icons/icon_menu.png);"));

        verticalLayout_4->addWidget(toggleButton);


        verticalMenuLayout->addWidget(toggleBox);

        topMenu = new QFrame(leftMenuFrame);
        topMenu->setObjectName("topMenu");
        topMenu->setFrameShape(QFrame::NoFrame);
        topMenu->setFrameShadow(QFrame::Raised);
        verticalLayout_8 = new QVBoxLayout(topMenu);
        verticalLayout_8->setSpacing(0);
        verticalLayout_8->setObjectName("verticalLayout_8");
        verticalLayout_8->setContentsMargins(0, 0, 0, 0);
        btn_home = new QPushButton(topMenu);
        btn_home->setObjectName("btn_home");
        sizePolicy.setHeightForWidth(btn_home->sizePolicy().hasHeightForWidth());
        btn_home->setSizePolicy(sizePolicy);
        btn_home->setMinimumSize(QSize(0, 45));
        btn_home->setFont(font);
        btn_home->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_home->setLayoutDirection(Qt::LeftToRight);
        btn_home->setStyleSheet(QString::fromUtf8("background-image: url(:/icons/images/icons/cil-home.png);"));

        verticalLayout_8->addWidget(btn_home);

        btn_widgets = new QPushButton(topMenu);
        btn_widgets->setObjectName("btn_widgets");
        sizePolicy.setHeightForWidth(btn_widgets->sizePolicy().hasHeightForWidth());
        btn_widgets->setSizePolicy(sizePolicy);
        btn_widgets->setMinimumSize(QSize(0, 45));
        btn_widgets->setFont(font);
        btn_widgets->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_widgets->setLayoutDirection(Qt::LeftToRight);
        btn_widgets->setStyleSheet(QString::fromUtf8("\n"
"background-image: url(:/icons/images/icons/cil-chart.png);"));

        verticalLayout_8->addWidget(btn_widgets);

        btn_ota = new QPushButton(topMenu);
        btn_ota->setObjectName("btn_ota");
        sizePolicy.setHeightForWidth(btn_ota->sizePolicy().hasHeightForWidth());
        btn_ota->setSizePolicy(sizePolicy);
        btn_ota->setMinimumSize(QSize(0, 45));
        btn_ota->setFont(font);
        btn_ota->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_ota->setLayoutDirection(Qt::LeftToRight);
        btn_ota->setStyleSheet(QString::fromUtf8("background-image: url(:/icons/images/icons/cil-arrow-circle-top.png);"));

        verticalLayout_8->addWidget(btn_ota);

        btn_document = new QPushButton(topMenu);
        btn_document->setObjectName("btn_document");
        sizePolicy.setHeightForWidth(btn_document->sizePolicy().hasHeightForWidth());
        btn_document->setSizePolicy(sizePolicy);
        btn_document->setMinimumSize(QSize(0, 45));
        btn_document->setFont(font);
        btn_document->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_document->setLayoutDirection(Qt::LeftToRight);
        btn_document->setStyleSheet(QString::fromUtf8("background-image: url(:/icons/images/icons/cil-file.png);"));

        verticalLayout_8->addWidget(btn_document);

        btn_exit = new QPushButton(topMenu);
        btn_exit->setObjectName("btn_exit");
        sizePolicy.setHeightForWidth(btn_exit->sizePolicy().hasHeightForWidth());
        btn_exit->setSizePolicy(sizePolicy);
        btn_exit->setMinimumSize(QSize(0, 45));
        btn_exit->setFont(font);
        btn_exit->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_exit->setLayoutDirection(Qt::LeftToRight);
        btn_exit->setStyleSheet(QString::fromUtf8("\n"
"background-image: url(:/icons/images/icons/cil-loop-circular.png);"));

        verticalLayout_8->addWidget(btn_exit);


        verticalMenuLayout->addWidget(topMenu, 0, Qt::AlignTop);

        bottomMenu = new QFrame(leftMenuFrame);
        bottomMenu->setObjectName("bottomMenu");
        bottomMenu->setFrameShape(QFrame::NoFrame);
        bottomMenu->setFrameShadow(QFrame::Raised);
        verticalLayout_9 = new QVBoxLayout(bottomMenu);
        verticalLayout_9->setSpacing(0);
        verticalLayout_9->setObjectName("verticalLayout_9");
        verticalLayout_9->setContentsMargins(0, 0, 0, 0);
        toggleLeftBox = new QPushButton(bottomMenu);
        toggleLeftBox->setObjectName("toggleLeftBox");
        sizePolicy.setHeightForWidth(toggleLeftBox->sizePolicy().hasHeightForWidth());
        toggleLeftBox->setSizePolicy(sizePolicy);
        toggleLeftBox->setMinimumSize(QSize(0, 45));
        toggleLeftBox->setFont(font);
        toggleLeftBox->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        toggleLeftBox->setLayoutDirection(Qt::LeftToRight);
        toggleLeftBox->setStyleSheet(QString::fromUtf8("background-image: url(:/icons/images/icons/icon_settings.png);"));

        verticalLayout_9->addWidget(toggleLeftBox);


        verticalMenuLayout->addWidget(bottomMenu, 0, Qt::AlignBottom);


        verticalLayout_3->addWidget(leftMenuFrame);


        appLayout->addWidget(leftMenuBg);

        extraLeftBox = new QFrame(bgApp);
        extraLeftBox->setObjectName("extraLeftBox");
        extraLeftBox->setMinimumSize(QSize(0, 0));
        extraLeftBox->setMaximumSize(QSize(0, ********));
        extraLeftBox->setFrameShape(QFrame::NoFrame);
        extraLeftBox->setFrameShadow(QFrame::Raised);
        extraColumLayout = new QVBoxLayout(extraLeftBox);
        extraColumLayout->setSpacing(0);
        extraColumLayout->setObjectName("extraColumLayout");
        extraColumLayout->setContentsMargins(0, 0, 0, 0);
        extraTopBg = new QFrame(extraLeftBox);
        extraTopBg->setObjectName("extraTopBg");
        extraTopBg->setMinimumSize(QSize(0, 50));
        extraTopBg->setMaximumSize(QSize(********, 50));
        extraTopBg->setFrameShape(QFrame::NoFrame);
        extraTopBg->setFrameShadow(QFrame::Raised);
        verticalLayout_5 = new QVBoxLayout(extraTopBg);
        verticalLayout_5->setSpacing(0);
        verticalLayout_5->setObjectName("verticalLayout_5");
        verticalLayout_5->setContentsMargins(0, 0, 0, 0);
        extraTopLayout = new QGridLayout();
        extraTopLayout->setObjectName("extraTopLayout");
        extraTopLayout->setHorizontalSpacing(10);
        extraTopLayout->setVerticalSpacing(0);
        extraTopLayout->setContentsMargins(10, -1, 10, -1);
        extraIcon = new QFrame(extraTopBg);
        extraIcon->setObjectName("extraIcon");
        extraIcon->setMinimumSize(QSize(20, 0));
        extraIcon->setMaximumSize(QSize(20, 20));
        extraIcon->setFrameShape(QFrame::NoFrame);
        extraIcon->setFrameShadow(QFrame::Raised);

        extraTopLayout->addWidget(extraIcon, 0, 0, 1, 1);

        extraLabel = new QLabel(extraTopBg);
        extraLabel->setObjectName("extraLabel");
        extraLabel->setMinimumSize(QSize(150, 0));

        extraTopLayout->addWidget(extraLabel, 0, 1, 1, 1);

        extraCloseColumnBtn = new QPushButton(extraTopBg);
        extraCloseColumnBtn->setObjectName("extraCloseColumnBtn");
        extraCloseColumnBtn->setMinimumSize(QSize(28, 28));
        extraCloseColumnBtn->setMaximumSize(QSize(28, 28));
        extraCloseColumnBtn->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/images/icons/icon_close.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        extraCloseColumnBtn->setIcon(icon);
        extraCloseColumnBtn->setIconSize(QSize(20, 20));

        extraTopLayout->addWidget(extraCloseColumnBtn, 0, 2, 1, 1);


        verticalLayout_5->addLayout(extraTopLayout);


        extraColumLayout->addWidget(extraTopBg);

        extraContent = new QFrame(extraLeftBox);
        extraContent->setObjectName("extraContent");
        extraContent->setFrameShape(QFrame::NoFrame);
        extraContent->setFrameShadow(QFrame::Raised);
        verticalLayout_12 = new QVBoxLayout(extraContent);
        verticalLayout_12->setSpacing(0);
        verticalLayout_12->setObjectName("verticalLayout_12");
        verticalLayout_12->setContentsMargins(0, 0, 0, 0);
        extraTopMenu = new QFrame(extraContent);
        extraTopMenu->setObjectName("extraTopMenu");
        extraTopMenu->setFrameShape(QFrame::NoFrame);
        extraTopMenu->setFrameShadow(QFrame::Raised);
        verticalLayout_11 = new QVBoxLayout(extraTopMenu);
        verticalLayout_11->setSpacing(0);
        verticalLayout_11->setObjectName("verticalLayout_11");
        verticalLayout_11->setContentsMargins(0, 0, 0, 0);
        btn_share = new QPushButton(extraTopMenu);
        btn_share->setObjectName("btn_share");
        sizePolicy.setHeightForWidth(btn_share->sizePolicy().hasHeightForWidth());
        btn_share->setSizePolicy(sizePolicy);
        btn_share->setMinimumSize(QSize(0, 45));
        btn_share->setFont(font);
        btn_share->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_share->setLayoutDirection(Qt::LeftToRight);
        btn_share->setStyleSheet(QString::fromUtf8("background-image: url(:/icons/images/icons/cil-share-boxed.png);"));

        verticalLayout_11->addWidget(btn_share);

        btn_adjustments = new QPushButton(extraTopMenu);
        btn_adjustments->setObjectName("btn_adjustments");
        sizePolicy.setHeightForWidth(btn_adjustments->sizePolicy().hasHeightForWidth());
        btn_adjustments->setSizePolicy(sizePolicy);
        btn_adjustments->setMinimumSize(QSize(0, 45));
        btn_adjustments->setFont(font);
        btn_adjustments->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_adjustments->setLayoutDirection(Qt::LeftToRight);
        btn_adjustments->setStyleSheet(QString::fromUtf8("background-image: url(:/icons/images/icons/cil-equalizer.png);"));

        verticalLayout_11->addWidget(btn_adjustments);

        btn_more = new QPushButton(extraTopMenu);
        btn_more->setObjectName("btn_more");
        sizePolicy.setHeightForWidth(btn_more->sizePolicy().hasHeightForWidth());
        btn_more->setSizePolicy(sizePolicy);
        btn_more->setMinimumSize(QSize(0, 45));
        btn_more->setFont(font);
        btn_more->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_more->setLayoutDirection(Qt::LeftToRight);
        btn_more->setStyleSheet(QString::fromUtf8("background-image: url(:/icons/images/icons/cil-layers.png);"));

        verticalLayout_11->addWidget(btn_more);


        verticalLayout_12->addWidget(extraTopMenu, 0, Qt::AlignTop);

        extraCenter = new QFrame(extraContent);
        extraCenter->setObjectName("extraCenter");
        extraCenter->setFrameShape(QFrame::NoFrame);
        extraCenter->setFrameShadow(QFrame::Raised);
        verticalLayout_10 = new QVBoxLayout(extraCenter);
        verticalLayout_10->setObjectName("verticalLayout_10");
        textEdit = new QTextEdit(extraCenter);
        textEdit->setObjectName("textEdit");
        textEdit->setMinimumSize(QSize(222, 0));
        textEdit->setStyleSheet(QString::fromUtf8("background: transparent;"));
        textEdit->setFrameShape(QFrame::NoFrame);
        textEdit->setReadOnly(true);

        verticalLayout_10->addWidget(textEdit);


        verticalLayout_12->addWidget(extraCenter);

        extraBottom = new QFrame(extraContent);
        extraBottom->setObjectName("extraBottom");
        extraBottom->setFrameShape(QFrame::NoFrame);
        extraBottom->setFrameShadow(QFrame::Raised);

        verticalLayout_12->addWidget(extraBottom);


        extraColumLayout->addWidget(extraContent);


        appLayout->addWidget(extraLeftBox);

        contentBox = new QFrame(bgApp);
        contentBox->setObjectName("contentBox");
        contentBox->setFrameShape(QFrame::NoFrame);
        contentBox->setFrameShadow(QFrame::Raised);
        verticalLayout_2 = new QVBoxLayout(contentBox);
        verticalLayout_2->setSpacing(0);
        verticalLayout_2->setObjectName("verticalLayout_2");
        verticalLayout_2->setContentsMargins(0, 0, 0, 0);
        contentTopBg = new QFrame(contentBox);
        contentTopBg->setObjectName("contentTopBg");
        contentTopBg->setMinimumSize(QSize(0, 50));
        contentTopBg->setMaximumSize(QSize(********, 50));
        contentTopBg->setFrameShape(QFrame::NoFrame);
        contentTopBg->setFrameShadow(QFrame::Raised);
        horizontalLayout = new QHBoxLayout(contentTopBg);
        horizontalLayout->setSpacing(0);
        horizontalLayout->setObjectName("horizontalLayout");
        horizontalLayout->setContentsMargins(0, 0, 10, 0);
        leftBox = new QFrame(contentTopBg);
        leftBox->setObjectName("leftBox");
        QSizePolicy sizePolicy1(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Preferred);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(leftBox->sizePolicy().hasHeightForWidth());
        leftBox->setSizePolicy(sizePolicy1);
        leftBox->setFrameShape(QFrame::NoFrame);
        leftBox->setFrameShadow(QFrame::Raised);
        horizontalLayout_3 = new QHBoxLayout(leftBox);
        horizontalLayout_3->setSpacing(0);
        horizontalLayout_3->setObjectName("horizontalLayout_3");
        horizontalLayout_3->setContentsMargins(0, 0, 0, 0);
        titleRightInfo = new QLabel(leftBox);
        titleRightInfo->setObjectName("titleRightInfo");
        QSizePolicy sizePolicy2(QSizePolicy::Policy::Preferred, QSizePolicy::Policy::Expanding);
        sizePolicy2.setHorizontalStretch(0);
        sizePolicy2.setVerticalStretch(0);
        sizePolicy2.setHeightForWidth(titleRightInfo->sizePolicy().hasHeightForWidth());
        titleRightInfo->setSizePolicy(sizePolicy2);
        titleRightInfo->setMaximumSize(QSize(********, 45));
        titleRightInfo->setFont(font);
        titleRightInfo->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter);

        horizontalLayout_3->addWidget(titleRightInfo);


        horizontalLayout->addWidget(leftBox);

        rightButtons = new QFrame(contentTopBg);
        rightButtons->setObjectName("rightButtons");
        rightButtons->setMinimumSize(QSize(0, 28));
        rightButtons->setFrameShape(QFrame::NoFrame);
        rightButtons->setFrameShadow(QFrame::Raised);
        horizontalLayout_2 = new QHBoxLayout(rightButtons);
        horizontalLayout_2->setSpacing(5);
        horizontalLayout_2->setObjectName("horizontalLayout_2");
        horizontalLayout_2->setContentsMargins(0, 0, 0, 0);
        settingsTopBtn = new QPushButton(rightButtons);
        settingsTopBtn->setObjectName("settingsTopBtn");
        settingsTopBtn->setMinimumSize(QSize(28, 28));
        settingsTopBtn->setMaximumSize(QSize(28, 28));
        settingsTopBtn->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/images/icons/icon_settings.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        settingsTopBtn->setIcon(icon1);
        settingsTopBtn->setIconSize(QSize(20, 20));

        horizontalLayout_2->addWidget(settingsTopBtn);

        minimizeAppBtn = new QPushButton(rightButtons);
        minimizeAppBtn->setObjectName("minimizeAppBtn");
        minimizeAppBtn->setMinimumSize(QSize(28, 28));
        minimizeAppBtn->setMaximumSize(QSize(28, 28));
        minimizeAppBtn->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/images/icons/icon_minimize.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        minimizeAppBtn->setIcon(icon2);
        minimizeAppBtn->setIconSize(QSize(20, 20));

        horizontalLayout_2->addWidget(minimizeAppBtn);

        maximizeRestoreAppBtn = new QPushButton(rightButtons);
        maximizeRestoreAppBtn->setObjectName("maximizeRestoreAppBtn");
        maximizeRestoreAppBtn->setMinimumSize(QSize(28, 28));
        maximizeRestoreAppBtn->setMaximumSize(QSize(28, 28));
        QFont font3;
        font3.setFamilies({QString::fromUtf8("\345\276\256\350\275\257\351\233\205\351\273\221")});
        font3.setPointSize(11);
        font3.setBold(false);
        font3.setItalic(false);
        font3.setStyleStrategy(QFont::PreferDefault);
        maximizeRestoreAppBtn->setFont(font3);
        maximizeRestoreAppBtn->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/icons/images/icons/icon_maximize.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        maximizeRestoreAppBtn->setIcon(icon3);
        maximizeRestoreAppBtn->setIconSize(QSize(20, 20));

        horizontalLayout_2->addWidget(maximizeRestoreAppBtn);

        closeAppBtn = new QPushButton(rightButtons);
        closeAppBtn->setObjectName("closeAppBtn");
        closeAppBtn->setMinimumSize(QSize(28, 28));
        closeAppBtn->setMaximumSize(QSize(28, 28));
        closeAppBtn->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        closeAppBtn->setIcon(icon);
        closeAppBtn->setIconSize(QSize(20, 20));

        horizontalLayout_2->addWidget(closeAppBtn);


        horizontalLayout->addWidget(rightButtons, 0, Qt::AlignRight);


        verticalLayout_2->addWidget(contentTopBg);

        contentBottom = new QFrame(contentBox);
        contentBottom->setObjectName("contentBottom");
        contentBottom->setFrameShape(QFrame::NoFrame);
        contentBottom->setFrameShadow(QFrame::Raised);
        verticalLayout_6 = new QVBoxLayout(contentBottom);
        verticalLayout_6->setSpacing(0);
        verticalLayout_6->setObjectName("verticalLayout_6");
        verticalLayout_6->setContentsMargins(0, 0, 0, 0);
        content = new QFrame(contentBottom);
        content->setObjectName("content");
        content->setFrameShape(QFrame::NoFrame);
        content->setFrameShadow(QFrame::Raised);
        horizontalLayout_4 = new QHBoxLayout(content);
        horizontalLayout_4->setSpacing(0);
        horizontalLayout_4->setObjectName("horizontalLayout_4");
        horizontalLayout_4->setContentsMargins(0, 0, 0, 0);
        pagesContainer = new QFrame(content);
        pagesContainer->setObjectName("pagesContainer");
        pagesContainer->setStyleSheet(QString::fromUtf8(""));
        pagesContainer->setFrameShape(QFrame::NoFrame);
        pagesContainer->setFrameShadow(QFrame::Raised);
        verticalLayout_15 = new QVBoxLayout(pagesContainer);
        verticalLayout_15->setSpacing(0);
        verticalLayout_15->setObjectName("verticalLayout_15");
        verticalLayout_15->setContentsMargins(10, 10, 10, 10);
        stackedWidget = new QStackedWidget(pagesContainer);
        stackedWidget->setObjectName("stackedWidget");
        stackedWidget->setStyleSheet(QString::fromUtf8("background: transparent;"));
        home = new QWidget();
        home->setObjectName("home");
        home->setStyleSheet(QString::fromUtf8(""));
        horizontalLayout_6 = new QHBoxLayout(home);
        horizontalLayout_6->setObjectName("horizontalLayout_6");
        textEdit_2 = new QTextEdit(home);
        textEdit_2->setObjectName("textEdit_2");
        sizePolicy1.setHeightForWidth(textEdit_2->sizePolicy().hasHeightForWidth());
        textEdit_2->setSizePolicy(sizePolicy1);
        textEdit_2->setMinimumSize(QSize(500, 700));
        textEdit_2->setMaximumSize(QSize(600, ********));
        textEdit_2->setFont(font);
        textEdit_2->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_6->addWidget(textEdit_2);

        frame_6 = new QFrame(home);
        frame_6->setObjectName("frame_6");
        frame_6->setMinimumSize(QSize(640, 700));
        frame_6->setStyleSheet(QString::fromUtf8("QFrame#frame_6{\n"
"	border: 1px solid #343b48;\n"
"	/*background-color: rgb(126, 199, 193);*/\n"
"	border-radius:20px;\n"
"\n"
"}\n"
"QFrame#frame_7{\n"
"	/*background-color: rgb(161, 217, 218);*/\n"
"	border-radius:20px;\n"
"\n"
"}\n"
"QFrame#frame_8{\n"
"	/*background-color: rgb(161, 217, 218);*/\n"
"	border-radius:20px;\n"
"\n"
"}\n"
"QPushButton {\n"
"	border-radius:20px;\n"
"	/*background-color: rgb(255, 255, 255);*/\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"	color: black;\n"
"	background-color: rgb(207, 232, 232);\n"
"	padding-left: 15px;\n"
"}\n"
"QPushButton:pressed {\n"
"	/*color: white;*/\n"
"	background-color: rgb(189, 147, 249);\n"
"	border-style: inset;\n"
"	padding-left: 15px;\n"
"}\n"
"QLineEdit{\n"
"	/*color: rgb(0, 0, 0);*/\n"
"	background-color: rgb(255, 255, 255);\n"
"	border-radius:15px;\n"
"}\n"
"QLabel#info_text{\n"
"	font-size:22px;\n"
"}"));
        frame_6->setFrameShape(QFrame::StyledPanel);
        frame_6->setFrameShadow(QFrame::Raised);
        verticalLayout_21 = new QVBoxLayout(frame_6);
        verticalLayout_21->setObjectName("verticalLayout_21");
        verticalLayout_21->setContentsMargins(5, 5, 7, 5);
        frame_7 = new QFrame(frame_6);
        frame_7->setObjectName("frame_7");
        QSizePolicy sizePolicy3(QSizePolicy::Policy::Preferred, QSizePolicy::Policy::Preferred);
        sizePolicy3.setHorizontalStretch(0);
        sizePolicy3.setVerticalStretch(0);
        sizePolicy3.setHeightForWidth(frame_7->sizePolicy().hasHeightForWidth());
        frame_7->setSizePolicy(sizePolicy3);
        frame_7->setStyleSheet(QString::fromUtf8(""));
        frame_7->setFrameShape(QFrame::StyledPanel);
        frame_7->setFrameShadow(QFrame::Raised);
        verticalLayout_22 = new QVBoxLayout(frame_7);
        verticalLayout_22->setObjectName("verticalLayout_22");
        horizontalLayout_8 = new QHBoxLayout();
        horizontalLayout_8->setObjectName("horizontalLayout_8");
        horizontalSpacer_5 = new QSpacerItem(230, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Minimum);

        horizontalLayout_8->addItem(horizontalSpacer_5);

        frame_9 = new QFrame(frame_7);
        frame_9->setObjectName("frame_9");
        QSizePolicy sizePolicy4(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Minimum);
        sizePolicy4.setHorizontalStretch(0);
        sizePolicy4.setVerticalStretch(0);
        sizePolicy4.setHeightForWidth(frame_9->sizePolicy().hasHeightForWidth());
        frame_9->setSizePolicy(sizePolicy4);
        frame_9->setMinimumSize(QSize(100, 40));
        frame_9->setMaximumSize(QSize(100, 40));
        frame_9->setFrameShape(QFrame::StyledPanel);
        frame_9->setFrameShadow(QFrame::Raised);
        horizontalLayout_7 = new QHBoxLayout(frame_9);
        horizontalLayout_7->setObjectName("horizontalLayout_7");
        horizontalLayout_7->setContentsMargins(20, 0, 30, 0);
        label_15 = new QLabel(frame_9);
        label_15->setObjectName("label_15");
        label_15->setMinimumSize(QSize(40, 40));
        label_15->setFont(font);
        label_15->setStyleSheet(QString::fromUtf8("image: url(:/icons/images/icons/cil-wallet.png);"));
        label_15->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        horizontalLayout_7->addWidget(label_15);

        info_text = new QLabel(frame_9);
        info_text->setObjectName("info_text");
        info_text->setMinimumSize(QSize(47, 35));
        QFont font4;
        font4.setFamilies({QString::fromUtf8("\345\276\256\350\275\257\351\233\205\351\273\221")});
        font4.setBold(false);
        font4.setItalic(false);
        info_text->setFont(font4);
        info_text->setAlignment(Qt::AlignCenter);

        horizontalLayout_7->addWidget(info_text);


        horizontalLayout_8->addWidget(frame_9);

        horizontalSpacer_6 = new QSpacerItem(230, 20, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Minimum);

        horizontalLayout_8->addItem(horizontalSpacer_6);


        verticalLayout_22->addLayout(horizontalLayout_8);

        horizontalLayout_16 = new QHBoxLayout();
        horizontalLayout_16->setSpacing(8);
        horizontalLayout_16->setObjectName("horizontalLayout_16");
        horizontalLayout_16->setContentsMargins(8, 0, 8, 0);
        verticalLayout_23 = new QVBoxLayout();
        verticalLayout_23->setSpacing(12);
        verticalLayout_23->setObjectName("verticalLayout_23");
        verticalLayout_23->setContentsMargins(12, 0, 12, 0);
        horizontalLayout_10 = new QHBoxLayout();
        horizontalLayout_10->setObjectName("horizontalLayout_10");
        horizontalLayout_10->setContentsMargins(-1, 5, -1, 5);
        label_10 = new QLabel(frame_7);
        label_10->setObjectName("label_10");
        label_10->setMinimumSize(QSize(85, 30));
        label_10->setFont(font);
        label_10->setAlignment(Qt::AlignCenter);

        horizontalLayout_10->addWidget(label_10);

        line_filename = new QLineEdit(frame_7);
        line_filename->setObjectName("line_filename");
        line_filename->setMinimumSize(QSize(284, 40));
        line_filename->setFont(font);
        line_filename->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_10->addWidget(line_filename);


        verticalLayout_23->addLayout(horizontalLayout_10);

        horizontalLayout_15 = new QHBoxLayout();
        horizontalLayout_15->setObjectName("horizontalLayout_15");
        horizontalLayout_15->setContentsMargins(-1, 5, -1, 5);
        label_11 = new QLabel(frame_7);
        label_11->setObjectName("label_11");
        label_11->setMinimumSize(QSize(85, 30));
        label_11->setFont(font);
        label_11->setAlignment(Qt::AlignCenter);

        horizontalLayout_15->addWidget(label_11);

        line_filepath = new QLineEdit(frame_7);
        line_filepath->setObjectName("line_filepath");
        line_filepath->setMinimumSize(QSize(284, 40));
        line_filepath->setFont(font);
        line_filepath->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_15->addWidget(line_filepath);


        verticalLayout_23->addLayout(horizontalLayout_15);

        horizontalLayout_17 = new QHBoxLayout();
        horizontalLayout_17->setObjectName("horizontalLayout_17");
        horizontalLayout_17->setContentsMargins(-1, 5, -1, 5);
        label_12 = new QLabel(frame_7);
        label_12->setObjectName("label_12");
        label_12->setMinimumSize(QSize(85, 30));
        label_12->setFont(font);
        label_12->setAlignment(Qt::AlignCenter);

        horizontalLayout_17->addWidget(label_12);

        line_data1 = new QLineEdit(frame_7);
        line_data1->setObjectName("line_data1");
        line_data1->setMinimumSize(QSize(284, 40));
        line_data1->setFont(font);
        line_data1->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_17->addWidget(line_data1);


        verticalLayout_23->addLayout(horizontalLayout_17);

        horizontalLayout_18 = new QHBoxLayout();
        horizontalLayout_18->setObjectName("horizontalLayout_18");
        horizontalLayout_18->setContentsMargins(-1, 5, -1, 5);
        label_13 = new QLabel(frame_7);
        label_13->setObjectName("label_13");
        label_13->setMinimumSize(QSize(85, 30));
        label_13->setFont(font);
        label_13->setAlignment(Qt::AlignCenter);

        horizontalLayout_18->addWidget(label_13);

        line_data2 = new QLineEdit(frame_7);
        line_data2->setObjectName("line_data2");
        line_data2->setMinimumSize(QSize(284, 40));
        line_data2->setFont(font);
        line_data2->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_18->addWidget(line_data2);


        verticalLayout_23->addLayout(horizontalLayout_18);

        horizontalLayout_19 = new QHBoxLayout();
        horizontalLayout_19->setObjectName("horizontalLayout_19");
        horizontalLayout_19->setContentsMargins(-1, 5, -1, 5);
        btn_get = new QPushButton(frame_7);
        btn_get->setObjectName("btn_get");
        btn_get->setMinimumSize(QSize(200, 45));
        btn_get->setFont(font);
        btn_get->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_get->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_19->addWidget(btn_get);

        btn_get_all_data = new QPushButton(frame_7);
        btn_get_all_data->setObjectName("btn_get_all_data");
        btn_get_all_data->setMinimumSize(QSize(200, 45));
        btn_get_all_data->setFont(font);
        btn_get_all_data->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_get_all_data->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_19->addWidget(btn_get_all_data);


        verticalLayout_23->addLayout(horizontalLayout_19);


        horizontalLayout_16->addLayout(verticalLayout_23);


        verticalLayout_22->addLayout(horizontalLayout_16);


        verticalLayout_21->addWidget(frame_7);

        frame_8 = new QFrame(frame_6);
        frame_8->setObjectName("frame_8");
        frame_8->setFont(font);
        frame_8->setStyleSheet(QString::fromUtf8(""));
        frame_8->setFrameShape(QFrame::StyledPanel);
        frame_8->setFrameShadow(QFrame::Raised);
        horizontalLayout_21 = new QHBoxLayout(frame_8);
        horizontalLayout_21->setObjectName("horizontalLayout_21");
        verticalLayout_24 = new QVBoxLayout();
        verticalLayout_24->setObjectName("verticalLayout_24");
        horizontalLayout_20 = new QHBoxLayout();
        horizontalLayout_20->setObjectName("horizontalLayout_20");
        horizontalSpacer_9 = new QSpacerItem(170, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_20->addItem(horizontalSpacer_9);

        frame_10 = new QFrame(frame_8);
        frame_10->setObjectName("frame_10");
        frame_10->setMinimumSize(QSize(200, 45));
        frame_10->setMaximumSize(QSize(********, 45));
        frame_10->setFrameShape(QFrame::StyledPanel);
        frame_10->setFrameShadow(QFrame::Raised);
        label_16 = new QLabel(frame_10);
        label_16->setObjectName("label_16");
        label_16->setGeometry(QRect(10, 10, 180, 30));
        label_16->setFont(font);
        label_16->setAlignment(Qt::AlignCenter);

        horizontalLayout_20->addWidget(frame_10);

        horizontalSpacer_10 = new QSpacerItem(170, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_20->addItem(horizontalSpacer_10);


        verticalLayout_24->addLayout(horizontalLayout_20);

        horizontalLayout_23 = new QHBoxLayout();
        horizontalLayout_23->setObjectName("horizontalLayout_23");
        horizontalLayout_23->setContentsMargins(5, 5, 5, 5);
        btn_chang_jump = new QPushButton(frame_8);
        btn_chang_jump->setObjectName("btn_chang_jump");
        btn_chang_jump->setMinimumSize(QSize(140, 45));
        btn_chang_jump->setFont(font);
        btn_chang_jump->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        horizontalLayout_23->addWidget(btn_chang_jump);

        btn_bat_jump = new QPushButton(frame_8);
        btn_bat_jump->setObjectName("btn_bat_jump");
        btn_bat_jump->setMinimumSize(QSize(140, 45));
        btn_bat_jump->setFont(font);
        btn_bat_jump->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        horizontalLayout_23->addWidget(btn_bat_jump);

        btn_time = new QPushButton(frame_8);
        btn_time->setObjectName("btn_time");
        btn_time->setMinimumSize(QSize(140, 45));
        btn_time->setFont(font);
        btn_time->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        horizontalLayout_23->addWidget(btn_time);


        verticalLayout_24->addLayout(horizontalLayout_23);

        horizontalLayout_24 = new QHBoxLayout();
        horizontalLayout_24->setObjectName("horizontalLayout_24");
        horizontalLayout_24->setContentsMargins(5, 5, 5, 5);
        btn_error = new QPushButton(frame_8);
        btn_error->setObjectName("btn_error");
        btn_error->setMinimumSize(QSize(140, 45));
        btn_error->setFont(font);
        btn_error->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        horizontalLayout_24->addWidget(btn_error);

        btn_status = new QPushButton(frame_8);
        btn_status->setObjectName("btn_status");
        btn_status->setMinimumSize(QSize(140, 45));
        btn_status->setFont(font);
        btn_status->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        horizontalLayout_24->addWidget(btn_status);

        btn_dazhaun = new QPushButton(frame_8);
        btn_dazhaun->setObjectName("btn_dazhaun");
        btn_dazhaun->setMinimumSize(QSize(140, 45));
        btn_dazhaun->setFont(font);
        btn_dazhaun->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        horizontalLayout_24->addWidget(btn_dazhaun);


        verticalLayout_24->addLayout(horizontalLayout_24);

        horizontalLayout_25 = new QHBoxLayout();
        horizontalLayout_25->setObjectName("horizontalLayout_25");
        horizontalLayout_25->setContentsMargins(5, 5, 5, 5);
        start_vol_cur = new QPushButton(frame_8);
        start_vol_cur->setObjectName("start_vol_cur");
        start_vol_cur->setMinimumSize(QSize(140, 45));
        start_vol_cur->setFont(font);
        start_vol_cur->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        horizontalLayout_25->addWidget(start_vol_cur);

        pushButton_8 = new QPushButton(frame_8);
        pushButton_8->setObjectName("pushButton_8");
        pushButton_8->setMinimumSize(QSize(140, 45));
        pushButton_8->setFont(font);
        pushButton_8->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        horizontalLayout_25->addWidget(pushButton_8);

        pushButton_10 = new QPushButton(frame_8);
        pushButton_10->setObjectName("pushButton_10");
        pushButton_10->setMinimumSize(QSize(140, 45));
        pushButton_10->setFont(font);
        pushButton_10->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        horizontalLayout_25->addWidget(pushButton_10);


        verticalLayout_24->addLayout(horizontalLayout_25);


        horizontalLayout_21->addLayout(verticalLayout_24);


        verticalLayout_21->addWidget(frame_8);


        horizontalLayout_6->addWidget(frame_6);

        stackedWidget->addWidget(home);
        widgets = new QWidget();
        widgets->setObjectName("widgets");
        widgets->setStyleSheet(QString::fromUtf8("b"));
        verticalLayout = new QVBoxLayout(widgets);
        verticalLayout->setObjectName("verticalLayout");
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        row_1 = new QFrame(widgets);
        row_1->setObjectName("row_1");
        sizePolicy2.setHeightForWidth(row_1->sizePolicy().hasHeightForWidth());
        row_1->setSizePolicy(sizePolicy2);
        row_1->setFrameShape(QFrame::StyledPanel);
        row_1->setFrameShadow(QFrame::Raised);
        verticalLayout_19 = new QVBoxLayout(row_1);
        verticalLayout_19->setObjectName("verticalLayout_19");
        frame_content_wid_1 = new QFrame(row_1);
        frame_content_wid_1->setObjectName("frame_content_wid_1");
        frame_content_wid_1->setFrameShape(QFrame::NoFrame);
        frame_content_wid_1->setFrameShadow(QFrame::Raised);
        horizontalLayout_9 = new QHBoxLayout(frame_content_wid_1);
        horizontalLayout_9->setObjectName("horizontalLayout_9");
        horizontalLayout_9->setContentsMargins(0, 0, 0, 0);
        lineEdit = new QLineEdit(frame_content_wid_1);
        lineEdit->setObjectName("lineEdit");
        lineEdit->setMinimumSize(QSize(450, 30));
        lineEdit->setStyleSheet(QString::fromUtf8("background-color: rgb(33, 37, 43);"));

        horizontalLayout_9->addWidget(lineEdit);

        btn_open_csv = new QPushButton(frame_content_wid_1);
        btn_open_csv->setObjectName("btn_open_csv");
        btn_open_csv->setMinimumSize(QSize(120, 35));
        btn_open_csv->setFont(font);
        btn_open_csv->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_open_csv->setStyleSheet(QString::fromUtf8("background-color: rgb(52, 59, 72);"));
        QIcon icon4;
        icon4.addFile(QString::fromUtf8(":/icons/images/icons/cil-folder-open.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        btn_open_csv->setIcon(icon4);

        horizontalLayout_9->addWidget(btn_open_csv);

        btn_combox_image = new QComboBox(frame_content_wid_1);
        btn_combox_image->setObjectName("btn_combox_image");
        btn_combox_image->setMinimumSize(QSize(120, 35));

        horizontalLayout_9->addWidget(btn_combox_image);

        btn_combox_image1 = new QComboBox(frame_content_wid_1);
        btn_combox_image1->setObjectName("btn_combox_image1");
        btn_combox_image1->setMinimumSize(QSize(120, 35));

        horizontalLayout_9->addWidget(btn_combox_image1);

        btn_create_image = new QPushButton(frame_content_wid_1);
        btn_create_image->setObjectName("btn_create_image");
        btn_create_image->setMinimumSize(QSize(120, 35));
        btn_create_image->setFont(font);
        btn_create_image->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_create_image->setStyleSheet(QString::fromUtf8("background-color: rgb(52, 59, 72);"));

        horizontalLayout_9->addWidget(btn_create_image);

        btn_clear_image = new QPushButton(frame_content_wid_1);
        btn_clear_image->setObjectName("btn_clear_image");
        btn_clear_image->setMinimumSize(QSize(120, 35));
        btn_clear_image->setStyleSheet(QString::fromUtf8("background-color: rgb(52, 59, 72);"));

        horizontalLayout_9->addWidget(btn_clear_image);


        verticalLayout_19->addWidget(frame_content_wid_1);


        verticalLayout->addWidget(row_1, 0, Qt::AlignTop);

        frame = new QFrame(widgets);
        frame->setObjectName("frame");
        sizePolicy2.setHeightForWidth(frame->sizePolicy().hasHeightForWidth());
        frame->setSizePolicy(sizePolicy2);
        frame->setMinimumSize(QSize(1150, 0));
        frame->setStyleSheet(QString::fromUtf8(""));
        frame->setFrameShape(QFrame::StyledPanel);
        frame->setFrameShadow(QFrame::Raised);
        verticalLayout_16 = new QVBoxLayout(frame);
        verticalLayout_16->setObjectName("verticalLayout_16");
        widget_2 = new QWidget(frame);
        widget_2->setObjectName("widget_2");
        widget_2->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        verticalLayout_16->addWidget(widget_2);


        verticalLayout->addWidget(frame);

        stackedWidget->addWidget(widgets);
        ota = new QWidget();
        ota->setObjectName("ota");
        horizontalLayout_27 = new QHBoxLayout(ota);
        horizontalLayout_27->setObjectName("horizontalLayout_27");
        frame_12 = new QFrame(ota);
        frame_12->setObjectName("frame_12");
        frame_12->setFrameShape(QFrame::StyledPanel);
        frame_12->setFrameShadow(QFrame::Raised);
        horizontalLayout_29 = new QHBoxLayout(frame_12);
        horizontalLayout_29->setObjectName("horizontalLayout_29");
        horizontalLayout_29->setContentsMargins(0, 0, 0, 0);
        verticalLayout_34 = new QVBoxLayout();
        verticalLayout_34->setObjectName("verticalLayout_34");
        horizontalLayout_22 = new QHBoxLayout();
        horizontalLayout_22->setObjectName("horizontalLayout_22");
        label_8 = new QLabel(frame_12);
        label_8->setObjectName("label_8");
        label_8->setMinimumSize(QSize(120, 35));
        label_8->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter);

        horizontalLayout_22->addWidget(label_8);

        horizontalSpacer_3 = new QSpacerItem(128, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_22->addItem(horizontalSpacer_3);

        hexShowing_checkBox = new QCheckBox(frame_12);
        hexShowing_checkBox->setObjectName("hexShowing_checkBox");
        QSizePolicy sizePolicy5(QSizePolicy::Policy::Preferred, QSizePolicy::Policy::Fixed);
        sizePolicy5.setHorizontalStretch(0);
        sizePolicy5.setVerticalStretch(0);
        sizePolicy5.setHeightForWidth(hexShowing_checkBox->sizePolicy().hasHeightForWidth());
        hexShowing_checkBox->setSizePolicy(sizePolicy5);

        horizontalLayout_22->addWidget(hexShowing_checkBox);

        ClearButton = new QPushButton(frame_12);
        ClearButton->setObjectName("ClearButton");
        ClearButton->setMinimumSize(QSize(120, 35));

        horizontalLayout_22->addWidget(ClearButton);


        verticalLayout_34->addLayout(horizontalLayout_22);

        textEdit_Recive = new QTextEdit(frame_12);
        textEdit_Recive->setObjectName("textEdit_Recive");
        sizePolicy3.setHeightForWidth(textEdit_Recive->sizePolicy().hasHeightForWidth());
        textEdit_Recive->setSizePolicy(sizePolicy3);
        textEdit_Recive->setMinimumSize(QSize(450, 645));
        textEdit_Recive->setStyleSheet(QString::fromUtf8("QTextEdit#textEdit_Recive{\n"
"	border-radius: 10px;\n"
"	border: 1px solid black;\n"
"	background-color: rgb(241, 241, 241);\n"
"}"));

        verticalLayout_34->addWidget(textEdit_Recive);


        horizontalLayout_29->addLayout(verticalLayout_34);


        horizontalLayout_27->addWidget(frame_12);

        frame_2 = new QFrame(ota);
        frame_2->setObjectName("frame_2");
        frame_2->setFrameShape(QFrame::StyledPanel);
        frame_2->setFrameShadow(QFrame::Raised);
        verticalLayout_30 = new QVBoxLayout(frame_2);
        verticalLayout_30->setObjectName("verticalLayout_30");
        verticalLayout_30->setContentsMargins(0, 0, 0, 0);
        horizontalLayout_26 = new QHBoxLayout();
        horizontalLayout_26->setObjectName("horizontalLayout_26");
        frame_5 = new QFrame(frame_2);
        frame_5->setObjectName("frame_5");
        frame_5->setMinimumSize(QSize(275, 280));
        frame_5->setFrameShape(QFrame::StyledPanel);
        frame_5->setFrameShadow(QFrame::Raised);
        horizontalLayout_13 = new QHBoxLayout(frame_5);
        horizontalLayout_13->setObjectName("horizontalLayout_13");
        horizontalLayout_13->setContentsMargins(0, 0, 0, 0);
        verticalLayout_17 = new QVBoxLayout();
        verticalLayout_17->setObjectName("verticalLayout_17");
        Com_Refresh_Label = new QLabel(frame_5);
        Com_Refresh_Label->setObjectName("Com_Refresh_Label");
        Com_Refresh_Label->setMinimumSize(QSize(120, 35));
        Com_Refresh_Label->setAlignment(Qt::AlignCenter);

        verticalLayout_17->addWidget(Com_Refresh_Label);

        Com_Baud_Label = new QLabel(frame_5);
        Com_Baud_Label->setObjectName("Com_Baud_Label");
        Com_Baud_Label->setMinimumSize(QSize(120, 35));
        Com_Baud_Label->setAlignment(Qt::AlignCenter);

        verticalLayout_17->addWidget(Com_Baud_Label);

        Com_Name_Label = new QLabel(frame_5);
        Com_Name_Label->setObjectName("Com_Name_Label");
        Com_Name_Label->setMinimumSize(QSize(120, 35));
        Com_Name_Label->setAlignment(Qt::AlignCenter);

        verticalLayout_17->addWidget(Com_Name_Label);

        Com_Open_Label = new QLabel(frame_5);
        Com_Open_Label->setObjectName("Com_Open_Label");
        Com_Open_Label->setMinimumSize(QSize(120, 35));
        Com_Open_Label->setAlignment(Qt::AlignCenter);

        verticalLayout_17->addWidget(Com_Open_Label);

        Com_Close_Label = new QLabel(frame_5);
        Com_Close_Label->setObjectName("Com_Close_Label");
        Com_Close_Label->setMinimumSize(QSize(120, 35));
        Com_Close_Label->setAlignment(Qt::AlignCenter);

        verticalLayout_17->addWidget(Com_Close_Label);


        horizontalLayout_13->addLayout(verticalLayout_17);

        verticalLayout_20 = new QVBoxLayout();
        verticalLayout_20->setObjectName("verticalLayout_20");
        Com_Refresh_Button = new QPushButton(frame_5);
        Com_Refresh_Button->setObjectName("Com_Refresh_Button");
        Com_Refresh_Button->setMinimumSize(QSize(120, 35));
        Com_Refresh_Button->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        verticalLayout_20->addWidget(Com_Refresh_Button);

        Com_Baud_Combo = new QComboBox(frame_5);
        Com_Baud_Combo->addItem(QString());
        Com_Baud_Combo->addItem(QString());
        Com_Baud_Combo->addItem(QString());
        Com_Baud_Combo->addItem(QString());
        Com_Baud_Combo->addItem(QString());
        Com_Baud_Combo->addItem(QString());
        Com_Baud_Combo->addItem(QString());
        Com_Baud_Combo->addItem(QString());
        Com_Baud_Combo->setObjectName("Com_Baud_Combo");
        Com_Baud_Combo->setMinimumSize(QSize(120, 35));
        Com_Baud_Combo->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        verticalLayout_20->addWidget(Com_Baud_Combo);

        Com_Name_Combo = new QComboBox(frame_5);
        Com_Name_Combo->setObjectName("Com_Name_Combo");
        Com_Name_Combo->setMinimumSize(QSize(120, 35));
        Com_Name_Combo->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        verticalLayout_20->addWidget(Com_Name_Combo);

        Com_Open_Button = new QPushButton(frame_5);
        Com_Open_Button->setObjectName("Com_Open_Button");
        Com_Open_Button->setMinimumSize(QSize(120, 35));
        Com_Open_Button->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        verticalLayout_20->addWidget(Com_Open_Button);

        Com_Close_Button = new QPushButton(frame_5);
        Com_Close_Button->setObjectName("Com_Close_Button");
        Com_Close_Button->setMinimumSize(QSize(120, 35));
        Com_Close_Button->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        verticalLayout_20->addWidget(Com_Close_Button);


        horizontalLayout_13->addLayout(verticalLayout_20);


        horizontalLayout_26->addWidget(frame_5);

        frame_11 = new QFrame(frame_2);
        frame_11->setObjectName("frame_11");
        frame_11->setMinimumSize(QSize(275, 280));
        frame_11->setFrameShape(QFrame::StyledPanel);
        frame_11->setFrameShadow(QFrame::Raised);
        verticalLayout_33 = new QVBoxLayout(frame_11);
        verticalLayout_33->setObjectName("verticalLayout_33");
        verticalLayout_33->setContentsMargins(0, 5, 0, 0);
        verticalLayout_28 = new QVBoxLayout();
        verticalLayout_28->setObjectName("verticalLayout_28");
        horizontalLayout_14 = new QHBoxLayout();
        horizontalLayout_14->setObjectName("horizontalLayout_14");
        verticalLayout_18 = new QVBoxLayout();
        verticalLayout_18->setObjectName("verticalLayout_18");
        label_9 = new QLabel(frame_11);
        label_9->setObjectName("label_9");
        label_9->setAlignment(Qt::AlignCenter);

        verticalLayout_18->addWidget(label_9);

        label_14 = new QLabel(frame_11);
        label_14->setObjectName("label_14");
        label_14->setAlignment(Qt::AlignCenter);

        verticalLayout_18->addWidget(label_14);

        label_17 = new QLabel(frame_11);
        label_17->setObjectName("label_17");
        label_17->setAlignment(Qt::AlignCenter);

        verticalLayout_18->addWidget(label_17);


        horizontalLayout_14->addLayout(verticalLayout_18);

        verticalLayout_26 = new QVBoxLayout();
        verticalLayout_26->setObjectName("verticalLayout_26");
        pushButton_2 = new QPushButton(frame_11);
        pushButton_2->setObjectName("pushButton_2");
        pushButton_2->setMinimumSize(QSize(120, 35));
        pushButton_2->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        verticalLayout_26->addWidget(pushButton_2);

        comboBox = new QComboBox(frame_11);
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->setObjectName("comboBox");
        comboBox->setMinimumSize(QSize(120, 35));

        verticalLayout_26->addWidget(comboBox);

        Com_Open_file = new QPushButton(frame_11);
        Com_Open_file->setObjectName("Com_Open_file");
        Com_Open_file->setMinimumSize(QSize(120, 35));
        Com_Open_file->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        verticalLayout_26->addWidget(Com_Open_file);


        horizontalLayout_14->addLayout(verticalLayout_26);


        verticalLayout_28->addLayout(horizontalLayout_14);

        verticalLayout_27 = new QVBoxLayout();
        verticalLayout_27->setObjectName("verticalLayout_27");
        Com_isOpenOrNot_Label = new QLabel(frame_11);
        Com_isOpenOrNot_Label->setObjectName("Com_isOpenOrNot_Label");
        Com_isOpenOrNot_Label->setMinimumSize(QSize(0, 35));
        Com_isOpenOrNot_Label->setAlignment(Qt::AlignCenter);

        verticalLayout_27->addWidget(Com_isOpenOrNot_Label);

        Com_file_line = new QLineEdit(frame_11);
        Com_file_line->setObjectName("Com_file_line");
        Com_file_line->setMinimumSize(QSize(0, 35));

        verticalLayout_27->addWidget(Com_file_line);

        progressBar = new QProgressBar(frame_11);
        progressBar->setObjectName("progressBar");
        progressBar->setMinimumSize(QSize(0, 35));
        progressBar->setLayoutDirection(Qt::LeftToRight);
        progressBar->setValue(24);
        progressBar->setOrientation(Qt::Horizontal);
        progressBar->setTextDirection(QProgressBar::TopToBottom);

        verticalLayout_27->addWidget(progressBar);


        verticalLayout_28->addLayout(verticalLayout_27);


        verticalLayout_33->addLayout(verticalLayout_28);


        horizontalLayout_26->addWidget(frame_11);


        verticalLayout_30->addLayout(horizontalLayout_26);

        verticalLayout_31 = new QVBoxLayout();
        verticalLayout_31->setObjectName("verticalLayout_31");
        frame_4 = new QFrame(frame_2);
        frame_4->setObjectName("frame_4");
        frame_4->setMinimumSize(QSize(610, 106));
        frame_4->setFrameShape(QFrame::StyledPanel);
        frame_4->setFrameShadow(QFrame::Raised);
        verticalLayout_29 = new QVBoxLayout(frame_4);
        verticalLayout_29->setObjectName("verticalLayout_29");
        verticalLayout_29->setContentsMargins(0, 0, 0, 0);
        Command_select = new QComboBox(frame_4);
        Command_select->addItem(QString());
        Command_select->addItem(QString());
        Command_select->setObjectName("Command_select");
        Command_select->setMinimumSize(QSize(0, 35));

        verticalLayout_29->addWidget(Command_select);

        lineEdit_3 = new QLineEdit(frame_4);
        lineEdit_3->setObjectName("lineEdit_3");
        lineEdit_3->setMinimumSize(QSize(0, 35));

        verticalLayout_29->addWidget(lineEdit_3);


        verticalLayout_31->addWidget(frame_4);

        verticalSpacer_2 = new QSpacerItem(20, 118, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout_31->addItem(verticalSpacer_2);

        frame_3 = new QFrame(frame_2);
        frame_3->setObjectName("frame_3");
        frame_3->setMinimumSize(QSize(630, 160));
        frame_3->setFrameShape(QFrame::StyledPanel);
        frame_3->setFrameShadow(QFrame::Raised);
        verticalLayout_32 = new QVBoxLayout(frame_3);
        verticalLayout_32->setObjectName("verticalLayout_32");
        verticalLayout_32->setContentsMargins(0, 0, 0, 0);
        horizontalLayout_11 = new QHBoxLayout();
        horizontalLayout_11->setObjectName("horizontalLayout_11");
        label_7 = new QLabel(frame_3);
        label_7->setObjectName("label_7");
        label_7->setMinimumSize(QSize(120, 35));
        label_7->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter);

        horizontalLayout_11->addWidget(label_7);

        horizontalSpacer = new QSpacerItem(208, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_11->addItem(horizontalSpacer);

        hexSending_checkBox = new QCheckBox(frame_3);
        hexSending_checkBox->setObjectName("hexSending_checkBox");
        sizePolicy5.setHeightForWidth(hexSending_checkBox->sizePolicy().hasHeightForWidth());
        hexSending_checkBox->setSizePolicy(sizePolicy5);

        horizontalLayout_11->addWidget(hexSending_checkBox);

        Send_Button = new QPushButton(frame_3);
        Send_Button->setObjectName("Send_Button");
        Send_Button->setMinimumSize(QSize(120, 35));

        horizontalLayout_11->addWidget(Send_Button);


        verticalLayout_32->addLayout(horizontalLayout_11);

        textEdit_Send = new QTextEdit(frame_3);
        textEdit_Send->setObjectName("textEdit_Send");
        sizePolicy3.setHeightForWidth(textEdit_Send->sizePolicy().hasHeightForWidth());
        textEdit_Send->setSizePolicy(sizePolicy3);
        textEdit_Send->setMinimumSize(QSize(520, 100));
        textEdit_Send->setStyleSheet(QString::fromUtf8("QTextEdit#textEdit_Send{\n"
"	border-radius: 20px;\n"
"	border: 1px solid black;\n"
"	background-color: rgb(241, 241, 241);\n"
"}"));

        verticalLayout_32->addWidget(textEdit_Send);


        verticalLayout_31->addWidget(frame_3);


        verticalLayout_30->addLayout(verticalLayout_31);


        horizontalLayout_27->addWidget(frame_2);

        stackedWidget->addWidget(ota);
        Real_time_charts = new QWidget();
        Real_time_charts->setObjectName("Real_time_charts");
        verticalLayout_36 = new QVBoxLayout(Real_time_charts);
        verticalLayout_36->setObjectName("verticalLayout_36");
        verticalLayout_36->setContentsMargins(0, 0, 0, 0);
        frame_13 = new QFrame(Real_time_charts);
        frame_13->setObjectName("frame_13");
        frame_13->setFrameShape(QFrame::StyledPanel);
        frame_13->setFrameShadow(QFrame::Raised);
        verticalLayout_35 = new QVBoxLayout(frame_13);
        verticalLayout_35->setObjectName("verticalLayout_35");
        verticalLayout_35->setContentsMargins(0, 0, 0, 0);
        verticalLayout_25 = new QVBoxLayout();
        verticalLayout_25->setObjectName("verticalLayout_25");
        horizontalLayout_28 = new QHBoxLayout();
        horizontalLayout_28->setObjectName("horizontalLayout_28");
        Chart_line = new QLineEdit(frame_13);
        Chart_line->setObjectName("Chart_line");
        Chart_line->setMinimumSize(QSize(1020, 35));

        horizontalLayout_28->addWidget(Chart_line);

        Chart_btn = new QPushButton(frame_13);
        Chart_btn->setObjectName("Chart_btn");
        Chart_btn->setMinimumSize(QSize(110, 35));
        Chart_btn->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));

        horizontalLayout_28->addWidget(Chart_btn);


        verticalLayout_25->addLayout(horizontalLayout_28);

        Chart_widget = new QWidget(frame_13);
        Chart_widget->setObjectName("Chart_widget");
        Chart_widget->setMinimumSize(QSize(1140, 650));
        Chart_widget->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        verticalLayout_25->addWidget(Chart_widget);


        verticalLayout_35->addLayout(verticalLayout_25);


        verticalLayout_36->addWidget(frame_13);

        stackedWidget->addWidget(Real_time_charts);

        verticalLayout_15->addWidget(stackedWidget);


        horizontalLayout_4->addWidget(pagesContainer);

        extraRightBox = new QFrame(content);
        extraRightBox->setObjectName("extraRightBox");
        extraRightBox->setMinimumSize(QSize(0, 0));
        extraRightBox->setMaximumSize(QSize(0, ********));
        extraRightBox->setFrameShape(QFrame::NoFrame);
        extraRightBox->setFrameShadow(QFrame::Raised);
        verticalLayout_7 = new QVBoxLayout(extraRightBox);
        verticalLayout_7->setSpacing(0);
        verticalLayout_7->setObjectName("verticalLayout_7");
        verticalLayout_7->setContentsMargins(0, 0, 0, 0);
        contentSettings = new QFrame(extraRightBox);
        contentSettings->setObjectName("contentSettings");
        contentSettings->setFrameShape(QFrame::NoFrame);
        contentSettings->setFrameShadow(QFrame::Raised);
        verticalLayout_13 = new QVBoxLayout(contentSettings);
        verticalLayout_13->setSpacing(0);
        verticalLayout_13->setObjectName("verticalLayout_13");
        verticalLayout_13->setContentsMargins(0, 0, 0, 0);
        topMenus = new QFrame(contentSettings);
        topMenus->setObjectName("topMenus");
        topMenus->setFrameShape(QFrame::NoFrame);
        topMenus->setFrameShadow(QFrame::Raised);
        verticalLayout_14 = new QVBoxLayout(topMenus);
        verticalLayout_14->setSpacing(0);
        verticalLayout_14->setObjectName("verticalLayout_14");
        verticalLayout_14->setContentsMargins(0, 0, 0, 0);
        btn_message = new QPushButton(topMenus);
        btn_message->setObjectName("btn_message");
        sizePolicy.setHeightForWidth(btn_message->sizePolicy().hasHeightForWidth());
        btn_message->setSizePolicy(sizePolicy);
        btn_message->setMinimumSize(QSize(0, 45));
        btn_message->setFont(font);
        btn_message->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_message->setLayoutDirection(Qt::LeftToRight);
        btn_message->setStyleSheet(QString::fromUtf8("background-image: url(:/icons/images/icons/cil-envelope-open.png);"));

        verticalLayout_14->addWidget(btn_message);

        btn_print = new QPushButton(topMenus);
        btn_print->setObjectName("btn_print");
        sizePolicy.setHeightForWidth(btn_print->sizePolicy().hasHeightForWidth());
        btn_print->setSizePolicy(sizePolicy);
        btn_print->setMinimumSize(QSize(0, 45));
        btn_print->setFont(font);
        btn_print->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_print->setLayoutDirection(Qt::LeftToRight);
        btn_print->setStyleSheet(QString::fromUtf8("background-image: url(:/icons/images/icons/cil-print.png);"));

        verticalLayout_14->addWidget(btn_print);

        btn_logout = new QPushButton(topMenus);
        btn_logout->setObjectName("btn_logout");
        sizePolicy.setHeightForWidth(btn_logout->sizePolicy().hasHeightForWidth());
        btn_logout->setSizePolicy(sizePolicy);
        btn_logout->setMinimumSize(QSize(0, 45));
        btn_logout->setFont(font);
        btn_logout->setCursor(QCursor(Qt::CursorShape::PointingHandCursor));
        btn_logout->setLayoutDirection(Qt::LeftToRight);
        btn_logout->setStyleSheet(QString::fromUtf8("background-image: url(:/icons/images/icons/cil-account-logout.png);"));

        verticalLayout_14->addWidget(btn_logout);


        verticalLayout_13->addWidget(topMenus, 0, Qt::AlignTop);


        verticalLayout_7->addWidget(contentSettings);


        horizontalLayout_4->addWidget(extraRightBox);


        verticalLayout_6->addWidget(content);

        bottomBar = new QFrame(contentBottom);
        bottomBar->setObjectName("bottomBar");
        bottomBar->setMinimumSize(QSize(0, 22));
        bottomBar->setMaximumSize(QSize(********, 22));
        bottomBar->setFrameShape(QFrame::NoFrame);
        bottomBar->setFrameShadow(QFrame::Raised);
        horizontalLayout_5 = new QHBoxLayout(bottomBar);
        horizontalLayout_5->setSpacing(0);
        horizontalLayout_5->setObjectName("horizontalLayout_5");
        horizontalLayout_5->setContentsMargins(0, 0, 0, 0);
        creditsLabel = new QLabel(bottomBar);
        creditsLabel->setObjectName("creditsLabel");
        creditsLabel->setMaximumSize(QSize(********, 16));
        creditsLabel->setFont(font4);
        creditsLabel->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter);

        horizontalLayout_5->addWidget(creditsLabel);

        version = new QLabel(bottomBar);
        version->setObjectName("version");
        version->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        horizontalLayout_5->addWidget(version);

        frame_size_grip = new QFrame(bottomBar);
        frame_size_grip->setObjectName("frame_size_grip");
        frame_size_grip->setMinimumSize(QSize(20, 0));
        frame_size_grip->setMaximumSize(QSize(20, ********));
        frame_size_grip->setFrameShape(QFrame::NoFrame);
        frame_size_grip->setFrameShadow(QFrame::Raised);

        horizontalLayout_5->addWidget(frame_size_grip);


        verticalLayout_6->addWidget(bottomBar);


        verticalLayout_2->addWidget(contentBottom);


        appLayout->addWidget(contentBox);


        horizontalLayout_12->addWidget(bgApp);

        MainWindow->setCentralWidget(styleSheet);

        retranslateUi(MainWindow);

        stackedWidget->setCurrentIndex(2);


        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "MainWindow", nullptr));
        titleLeftApp->setText(QCoreApplication::translate("MainWindow", "\350\275\257\344\273\266\345\217\212\346\225\264\346\234\272", nullptr));
        titleLeftDescription->setText(QCoreApplication::translate("MainWindow", "<html><head/><body><p>R&amp;DC-SCG-TOOL</p></body></html>", nullptr));
        toggleButton->setText(QCoreApplication::translate("MainWindow", "Hide", nullptr));
        btn_home->setText(QCoreApplication::translate("MainWindow", "\344\270\273\350\246\201\345\212\237\350\203\275", nullptr));
        btn_widgets->setText(QCoreApplication::translate("MainWindow", "\346\225\260\346\215\256\345\233\276\350\241\250", nullptr));
        btn_ota->setText(QCoreApplication::translate("MainWindow", "OTA", nullptr));
        btn_document->setText(QCoreApplication::translate("MainWindow", "\346\226\207\346\241\243", nullptr));
        btn_exit->setText(QCoreApplication::translate("MainWindow", "style", nullptr));
        toggleLeftBox->setText(QCoreApplication::translate("MainWindow", "Left Box", nullptr));
        extraLabel->setText(QCoreApplication::translate("MainWindow", "Left Box", nullptr));
#if QT_CONFIG(tooltip)
        extraCloseColumnBtn->setToolTip(QCoreApplication::translate("MainWindow", "Close left box", nullptr));
#endif // QT_CONFIG(tooltip)
        extraCloseColumnBtn->setText(QString());
        btn_share->setText(QCoreApplication::translate("MainWindow", "Share", nullptr));
        btn_adjustments->setText(QCoreApplication::translate("MainWindow", "Adjustments", nullptr));
        btn_more->setText(QCoreApplication::translate("MainWindow", "More", nullptr));
        textEdit->setHtml(QCoreApplication::translate("MainWindow", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:'\345\276\256\350\275\257\351\233\205\351\273\221'; font-size:11pt; font-weight:400; font-style:normal;\">\n"
"<p align=\"center\" style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:'Segoe UI'; font-size:12pt; color:#ff00ff;\">\345\267\245\345\205\267\351\200\202\347\224\250\350\214\203\345\233\264</span></p>\n"
"<p align=\"center\" style=\" margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:'Segoe UI'; font-size:10pt;\">\351\200\202\347\224\250\344\272\216\350\275\257\344\273\266\345\217\212\346\225\264\346\234\272\346\265\213\350\257\225</span></p>\n"
"<p align=\"center\""
                        " style=\" margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:'Segoe UI'; font-size:10pt;\">\345\244\204\347\220\206\345\205\205\347\224\265\346\227\266\351\225\277\343\200\201\347\255\233\351\200\211\346\225\260\346\215\256</span></p>\n"
"<p align=\"center\" style=\" margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:'Segoe UI'; font-size:10pt;\">\347\255\233\351\200\211\345\271\266\347\224\237\346\210\220\346\233\262\347\272\277\345\233\276</span></p>\n"
"<p align=\"center\" style=\" margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:'Segoe UI'; font-size:12pt; color:#ff55ff;\">\345\205\266\344\273\226\345\212\237\350\203\275</span></p>\n"
"<p align=\"center\" style=\" margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-ind"
                        "ent:0; text-indent:0px;\"><span style=\" font-family:'Segoe UI'; font-size:9pt; color:#ff55ff;\">By\357\274\232An--\345\217\257\344\273\245\346\226\260\345\242\236\345\212\237\350\203\275</span></p></body></html>", nullptr));
        titleRightInfo->setText(QCoreApplication::translate("MainWindow", "RDC-\347\240\224\345\217\221\351\203\250-\346\265\213\350\257\225\347\273\204-\346\225\260\346\215\256\345\244\204\347\220\206\345\267\245\345\205\267", nullptr));
#if QT_CONFIG(tooltip)
        settingsTopBtn->setToolTip(QCoreApplication::translate("MainWindow", "Settings", nullptr));
#endif // QT_CONFIG(tooltip)
        settingsTopBtn->setText(QString());
#if QT_CONFIG(tooltip)
        minimizeAppBtn->setToolTip(QCoreApplication::translate("MainWindow", "Minimize", nullptr));
#endif // QT_CONFIG(tooltip)
        minimizeAppBtn->setText(QString());
#if QT_CONFIG(tooltip)
        maximizeRestoreAppBtn->setToolTip(QCoreApplication::translate("MainWindow", "Maximize", nullptr));
#endif // QT_CONFIG(tooltip)
        maximizeRestoreAppBtn->setText(QString());
#if QT_CONFIG(tooltip)
        closeAppBtn->setToolTip(QCoreApplication::translate("MainWindow", "Close", nullptr));
#endif // QT_CONFIG(tooltip)
        closeAppBtn->setText(QString());
        textEdit_2->setPlaceholderText(QCoreApplication::translate("MainWindow", "\346\225\260\346\215\256\345\261\225\347\244\272", nullptr));
        label_15->setText(QString());
        info_text->setText(QCoreApplication::translate("MainWindow", "Info", nullptr));
        label_10->setText(QCoreApplication::translate("MainWindow", "\344\277\235\345\255\230 \350\267\257\345\276\204", nullptr));
#if QT_CONFIG(tooltip)
        line_filename->setToolTip(QCoreApplication::translate("MainWindow", "\345\244\232\344\270\252\350\267\257\345\276\204\347\232\204\346\203\205\345\206\265\344\270\213\346\263\250\346\204\217\351\207\215\346\226\260\345\221\275\345\220\215\346\226\207\344\273\266", nullptr));
#endif // QT_CONFIG(tooltip)
        line_filename->setPlaceholderText(QCoreApplication::translate("MainWindow", "\351\273\230\350\256\244\345\267\245\345\205\267\346\224\276\347\275\256\350\267\257\345\276\204\344\270\213\347\232\204data.csv", nullptr));
        label_11->setText(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266 \350\267\257\345\276\204", nullptr));
        line_filepath->setPlaceholderText(QCoreApplication::translate("MainWindow", "\351\234\200\350\246\201\346\211\223\345\274\200\347\232\204\346\225\260\346\215\256\346\226\207\344\273\266", nullptr));
        label_12->setText(QCoreApplication::translate("MainWindow", "\346\225\260\346\215\256\346\235\241\344\273\2661", nullptr));
#if QT_CONFIG(tooltip)
        line_data1->setToolTip(QCoreApplication::translate("MainWindow", "\346\263\250\346\204\217\344\270\215\350\246\201<>", nullptr));
#endif // QT_CONFIG(tooltip)
        line_data1->setPlaceholderText(QCoreApplication::translate("MainWindow", "motor", nullptr));
        label_13->setText(QCoreApplication::translate("MainWindow", "\346\225\260\346\215\256\346\235\241\344\273\2662", nullptr));
#if QT_CONFIG(tooltip)
        line_data2->setToolTip(QCoreApplication::translate("MainWindow", "\346\263\250\346\204\217\344\270\215\350\246\201<>", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(statustip)
        line_data2->setStatusTip(QString());
#endif // QT_CONFIG(statustip)
        line_data2->setPlaceholderText(QCoreApplication::translate("MainWindow", "imu", nullptr));
        btn_get->setText(QCoreApplication::translate("MainWindow", "\350\216\267\345\217\226\346\226\207\344\273\266\350\267\257\345\276\204", nullptr));
        btn_get_all_data->setText(QCoreApplication::translate("MainWindow", "\350\216\267\345\217\226\346\225\260\346\215\256", nullptr));
        label_16->setText(QCoreApplication::translate("MainWindow", "\345\205\266\344\273\226\345\212\237\350\203\275\345\217\202\347\205\247\344\270\212\350\277\260\347\255\233\346\237\245", nullptr));
#if QT_CONFIG(tooltip)
        btn_chang_jump->setToolTip(QCoreApplication::translate("MainWindow", "<html><head/><body><p>info\347\232\204\350\267\263\347\224\265\346\203\205\345\206\265</p></body></html>", nullptr));
#endif // QT_CONFIG(tooltip)
        btn_chang_jump->setText(QCoreApplication::translate("MainWindow", "Info\347\224\265\351\207\217", nullptr));
#if QT_CONFIG(tooltip)
        btn_bat_jump->setToolTip(QCoreApplication::translate("MainWindow", "bat\345\221\275\344\273\244\347\224\265\351\207\217\350\267\263\347\224\265\346\203\205\345\206\265", nullptr));
#endif // QT_CONFIG(tooltip)
        btn_bat_jump->setText(QCoreApplication::translate("MainWindow", "bat\347\224\265\351\207\217", nullptr));
#if QT_CONFIG(tooltip)
        btn_time->setToolTip(QCoreApplication::translate("MainWindow", "info\345\205\205\346\224\276\347\224\265\346\227\266\351\225\277", nullptr));
#endif // QT_CONFIG(tooltip)
        btn_time->setText(QCoreApplication::translate("MainWindow", "\345\205\205\346\224\276\347\224\265\346\227\266\351\225\277", nullptr));
#if QT_CONFIG(tooltip)
        btn_error->setToolTip(QCoreApplication::translate("MainWindow", "<html><head/><body><p>info\345\221\212\350\255\246<br/>wip\343\200\201wop\347\255\211</p></body></html>", nullptr));
#endif // QT_CONFIG(tooltip)
        btn_error->setText(QCoreApplication::translate("MainWindow", "\346\243\200\346\237\245\345\221\212\350\255\246", nullptr));
#if QT_CONFIG(tooltip)
        btn_status->setToolTip(QCoreApplication::translate("MainWindow", "<html><head/><body><p>\345\205\205\347\224\265\347\212\266\346\200\201\346\243\200\346\265\213</p></body></html>", nullptr));
#endif // QT_CONFIG(tooltip)
        btn_status->setText(QCoreApplication::translate("MainWindow", "\345\205\205\347\224\265\347\212\266\346\200\201", nullptr));
        btn_dazhaun->setText(QCoreApplication::translate("MainWindow", "\350\267\257\345\276\204\346\211\223\350\275\254", nullptr));
        start_vol_cur->setText(QCoreApplication::translate("MainWindow", "Start-Ent\345\217\202\346\225\260", nullptr));
        pushButton_8->setText(QCoreApplication::translate("MainWindow", "\347\224\265\345\216\213\347\250\263\345\256\232\346\200\247", nullptr));
        pushButton_10->setText(QCoreApplication::translate("MainWindow", "\347\224\265\346\265\201\347\250\263\345\256\232\346\200\247", nullptr));
        lineEdit->setText(QString());
        lineEdit->setPlaceholderText(QCoreApplication::translate("MainWindow", "PATH", nullptr));
        btn_open_csv->setText(QCoreApplication::translate("MainWindow", "Open", nullptr));
        btn_create_image->setText(QCoreApplication::translate("MainWindow", "\347\224\237\346\210\220", nullptr));
        btn_clear_image->setText(QCoreApplication::translate("MainWindow", "\346\270\205\351\231\244", nullptr));
        label_8->setText(QCoreApplication::translate("MainWindow", "\346\216\245\346\224\266\347\253\257", nullptr));
        hexShowing_checkBox->setText(QCoreApplication::translate("MainWindow", "16\350\277\233\345\210\266", nullptr));
        ClearButton->setText(QCoreApplication::translate("MainWindow", "\346\270\205\351\231\244", nullptr));
        Com_Refresh_Label->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243\346\220\234\347\264\242", nullptr));
        Com_Baud_Label->setText(QCoreApplication::translate("MainWindow", "\346\263\242\347\211\271\347\216\207", nullptr));
        Com_Name_Label->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243\351\200\211\346\213\251", nullptr));
        Com_Open_Label->setText(QCoreApplication::translate("MainWindow", "\346\211\223\345\274\200\344\270\262\345\217\243", nullptr));
        Com_Close_Label->setText(QCoreApplication::translate("MainWindow", "\345\205\263\351\227\255\344\270\262\345\217\243", nullptr));
        Com_Refresh_Button->setText(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260", nullptr));
        Com_Baud_Combo->setItemText(0, QCoreApplication::translate("MainWindow", "1200", nullptr));
        Com_Baud_Combo->setItemText(1, QCoreApplication::translate("MainWindow", "9600", nullptr));
        Com_Baud_Combo->setItemText(2, QCoreApplication::translate("MainWindow", "14400", nullptr));
        Com_Baud_Combo->setItemText(3, QCoreApplication::translate("MainWindow", "19200", nullptr));
        Com_Baud_Combo->setItemText(4, QCoreApplication::translate("MainWindow", "38400", nullptr));
        Com_Baud_Combo->setItemText(5, QCoreApplication::translate("MainWindow", "57600", nullptr));
        Com_Baud_Combo->setItemText(6, QCoreApplication::translate("MainWindow", "115200", nullptr));
        Com_Baud_Combo->setItemText(7, QCoreApplication::translate("MainWindow", "230400", nullptr));

        Com_Open_Button->setText(QCoreApplication::translate("MainWindow", "\346\211\223\345\274\200", nullptr));
        Com_Close_Button->setText(QCoreApplication::translate("MainWindow", "\345\205\263\351\227\255", nullptr));
        label_9->setText(QCoreApplication::translate("MainWindow", "\345\215\207\347\272\247", nullptr));
        label_14->setText(QCoreApplication::translate("MainWindow", "\351\200\237\347\216\207", nullptr));
        label_17->setText(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266\350\267\257\345\276\204", nullptr));
        pushButton_2->setText(QCoreApplication::translate("MainWindow", "\345\274\200\345\247\213\345\215\207\347\272\247", nullptr));
        comboBox->setItemText(0, QCoreApplication::translate("MainWindow", "128", nullptr));
        comboBox->setItemText(1, QCoreApplication::translate("MainWindow", "1024", nullptr));

        Com_Open_file->setText(QCoreApplication::translate("MainWindow", "\346\211\223\345\274\200\346\226\207\344\273\266", nullptr));
        Com_isOpenOrNot_Label->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243\345\276\205\346\211\223\345\274\200", nullptr));
        Command_select->setItemText(0, QCoreApplication::translate("MainWindow", "motor", nullptr));
        Command_select->setItemText(1, QCoreApplication::translate("MainWindow", "meas", nullptr));

        lineEdit_3->setPlaceholderText(QCoreApplication::translate("MainWindow", "\345\221\275\344\273\244\345\217\202\346\225\260", nullptr));
        label_7->setText(QCoreApplication::translate("MainWindow", "\345\217\221\351\200\201\347\253\257", nullptr));
        hexSending_checkBox->setText(QCoreApplication::translate("MainWindow", "16\350\277\233\345\210\266", nullptr));
        Send_Button->setText(QCoreApplication::translate("MainWindow", "\345\217\221\351\200\201", nullptr));
        Chart_btn->setText(QCoreApplication::translate("MainWindow", "\345\217\221\351\200\201", nullptr));
        btn_message->setText(QCoreApplication::translate("MainWindow", "Message", nullptr));
        btn_print->setText(QCoreApplication::translate("MainWindow", "Print", nullptr));
        btn_logout->setText(QCoreApplication::translate("MainWindow", "Logout", nullptr));
        creditsLabel->setText(QCoreApplication::translate("MainWindow", "By: Qing An", nullptr));
        version->setText(QCoreApplication::translate("MainWindow", "v1.0.9", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAIN_H
