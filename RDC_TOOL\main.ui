<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author><PERSON><PERSON><PERSON></author>
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1280</width>
    <height>833</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>940</width>
    <height>560</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="styleSheet">
   <property name="font">
    <font>
     <family>微软雅黑</family>
     <pointsize>11</pointsize>
     <weight>50</weight>
     <italic>false</italic>
     <bold>false</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">/* /////////////////////////////////////////////////////////////////////////////////////////////////

SET APP STYLESHEET - FULL STYLES HERE
DARK THEME - DRACULA COLOR BASED

///////////////////////////////////////////////////////////////////////////////////////////////// */

QWidget{
	color: rgb(221, 221, 221);
	font: 11pt &quot;微软雅黑&quot;;
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Tooltip */
QToolTip {
	color: #ffffff;
	background-color: rgba(33, 37, 43, 180);
	border: 1px solid rgb(44, 49, 58);
	background-image: none;
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 2px solid rgb(255, 121, 198);
	text-align: left;
	padding-left: 8px;
	margin: 0px;
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Bg App */
#bgApp {	
	background-color: rgb(40, 44, 52);
	border: 1px solid rgb(44, 49, 58);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Left Menu */
#leftMenuBg {	
	background-color: rgb(33, 37, 43);
}
#topLogo {
	background-color: rgb(33, 37, 43);
	background-image: url(:/images/images/images/PyDracula.png);
	background-position: centered;
	background-repeat: no-repeat;
}
#titleLeftApp { font: 63 12pt &quot;Segoe UI Semibold&quot;; }
#titleLeftDescription { font: 8pt &quot;Segoe UI&quot;; color: rgb(189, 147, 249); }

/* MENUS */
#topMenu .QPushButton {	
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 22px solid transparent;
	background-color: transparent;
	text-align: left;
	padding-left: 44px;
}
#topMenu .QPushButton:hover {
	background-color: rgb(40, 44, 52);
}
#topMenu .QPushButton:pressed {	
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}
#bottomMenu .QPushButton {	
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 20px solid transparent;
	background-color:transparent;
	text-align: left;
	padding-left: 44px;
}
#bottomMenu .QPushButton:hover {
	background-color: rgb(40, 44, 52);
}
#bottomMenu .QPushButton:pressed {	
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}
#leftMenuFrame{
	border-top: 3px solid rgb(44, 49, 58);
}

/* Toggle Button */
#toggleButton {
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 20px solid transparent;
	background-color: rgb(37, 41, 48);
	text-align: left;
	padding-left: 44px;
	color: rgb(113, 126, 149);
}
#toggleButton:hover {
	background-color: rgb(40, 44, 52);
}
#toggleButton:pressed {
	background-color: rgb(189, 147, 249);
}

/* Title Menu */
#titleRightInfo { padding-left: 10px; }


/* /////////////////////////////////////////////////////////////////////////////////////////////////
Extra Tab */
#extraLeftBox {	
	background-color: rgb(44, 49, 58);
}
#extraTopBg{	
	background-color: rgb(189, 147, 249)
}

/* Icon */
#extraIcon {
	background-position: center;
	background-repeat: no-repeat;
	background-image: url(:/icons/images/icons/icon_settings.png);
}

/* Label */
#extraLabel { color: rgb(255, 255, 255); }

/* Btn Close */
#extraCloseColumnBtn { background-color: rgba(255, 255, 255, 0); border: none;  border-radius: 5px; }
#extraCloseColumnBtn:hover { background-color: rgb(196, 161, 249); border-style: solid; border-radius: 4px; }
#extraCloseColumnBtn:pressed { background-color: rgb(180, 141, 238); border-style: solid; border-radius: 4px; }

/* Extra Content */
#extraContent{
	border-top: 3px solid rgb(40, 44, 52);
}

/* Extra Top Menus */
#extraTopMenu .QPushButton {
background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 22px solid transparent;
	background-color:transparent;
	text-align: left;
	padding-left: 44px;
}
#extraTopMenu .QPushButton:hover {
	background-color: rgb(40, 44, 52);
}
#extraTopMenu .QPushButton:pressed {	
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Content App */
#contentTopBg{	
	background-color: rgb(33, 37, 43);
}
#contentBottom{
	border-top: 3px solid rgb(44, 49, 58);
}

/* Top Buttons */
#rightButtons .QPushButton { background-color: rgba(255, 255, 255, 0); border: none;  border-radius: 5px; }
#rightButtons .QPushButton:hover { background-color: rgb(44, 49, 57); border-style: solid; border-radius: 4px; }
#rightButtons .QPushButton:pressed { background-color: rgb(23, 26, 30); border-style: solid; border-radius: 4px; }

/* Theme Settings */
#extraRightBox { background-color: rgb(44, 49, 58); }
#themeSettingsTopDetail { background-color: rgb(189, 147, 249); }

/* Bottom Bar */
#bottomBar { background-color: rgb(44, 49, 58); }
#bottomBar QLabel { font-size: 11px; color: rgb(113, 126, 149); padding-left: 10px; padding-right: 10px; padding-bottom: 2px; }

/* CONTENT SETTINGS */
/* MENUS */
#contentSettings .QPushButton {	
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 22px solid transparent;
	background-color:transparent;
	text-align: left;
	padding-left: 44px;
}
#contentSettings .QPushButton:hover {
	background-color: rgb(40, 44, 52);
}
#contentSettings .QPushButton:pressed {	
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
QTableWidget */
QTableWidget {	
	background-color: transparent;
	padding: 10px;
	border-radius: 5px;
	gridline-color: rgb(44, 49, 58);
	border-bottom: 1px solid rgb(44, 49, 60);
}
QTableWidget::item{
	border-color: rgb(44, 49, 60);
	padding-left: 5px;
	padding-right: 5px;
	gridline-color: rgb(44, 49, 60);
}
QTableWidget::item:selected{
	background-color: rgb(189, 147, 249);
}
QHeaderView::section{
	background-color: rgb(33, 37, 43);
	max-width: 30px;
	border: 1px solid rgb(44, 49, 58);
	border-style: none;
    border-bottom: 1px solid rgb(44, 49, 60);
    border-right: 1px solid rgb(44, 49, 60);
}
QTableWidget::horizontalHeader {	
	background-color: rgb(33, 37, 43);
}
QHeaderView::section:horizontal
{
    border: 1px solid rgb(33, 37, 43);
	background-color: rgb(33, 37, 43);
	padding: 3px;
	border-top-left-radius: 7px;
    border-top-right-radius: 7px;
}
QHeaderView::section:vertical
{
    border: 1px solid rgb(44, 49, 60);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
LineEdit */
QLineEdit {
	background-color: rgb(33, 37, 43);
	border-radius: 5px;
	border: 2px solid rgb(33, 37, 43);
	padding-left: 10px;
	selection-color: rgb(255, 255, 255);
	selection-background-color: rgb(255, 121, 198);
}
QLineEdit:hover {
	border: 2px solid rgb(64, 71, 88);
}
QLineEdit:focus {
	border: 2px solid rgb(91, 101, 124);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
PlainTextEdit */
QPlainTextEdit {
	background-color: rgb(27, 29, 35);
	border-radius: 5px;
	padding: 10px;
	selection-color: rgb(255, 255, 255);
	selection-background-color: rgb(255, 121, 198);
}
QPlainTextEdit  QScrollBar:vertical {
    width: 8px;
 }
QPlainTextEdit  QScrollBar:horizontal {
    height: 8px;
 }
QPlainTextEdit:hover {
	border: 2px solid rgb(64, 71, 88);
}
QPlainTextEdit:focus {
	border: 2px solid rgb(91, 101, 124);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
ScrollBars */
QScrollBar:horizontal {
    border: none;
    background: rgb(52, 59, 72);
    height: 8px;
    margin: 0px 21px 0 21px;
	border-radius: 0px;
}
QScrollBar::handle:horizontal {
    background: rgb(189, 147, 249);
    min-width: 25px;
	border-radius: 4px
}
QScrollBar::add-line:horizontal {
    border: none;
    background: rgb(55, 63, 77);
    width: 20px;
	border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    subcontrol-position: right;
    subcontrol-origin: margin;
}
QScrollBar::sub-line:horizontal {
    border: none;
    background: rgb(55, 63, 77);
    width: 20px;
	border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    subcontrol-position: left;
    subcontrol-origin: margin;
}
QScrollBar::up-arrow:horizontal, QScrollBar::down-arrow:horizontal
{
     background: none;
}
QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal
{
     background: none;
}
 QScrollBar:vertical {
	border: none;
    background: rgb(52, 59, 72);
    width: 8px;
    margin: 21px 0 21px 0;
	border-radius: 0px;
 }
 QScrollBar::handle:vertical {	
	background: rgb(189, 147, 249);
    min-height: 25px;
	border-radius: 4px
 }
 QScrollBar::add-line:vertical {
     border: none;
    background: rgb(55, 63, 77);
     height: 20px;
	border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
     subcontrol-position: bottom;
     subcontrol-origin: margin;
 }
 QScrollBar::sub-line:vertical {
	border: none;
    background: rgb(55, 63, 77);
     height: 20px;
	border-top-left-radius: 4px;
    border-top-right-radius: 4px;
     subcontrol-position: top;
     subcontrol-origin: margin;
 }
 QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
     background: none;
 }

 QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
     background: none;
 }

/* /////////////////////////////////////////////////////////////////////////////////////////////////
CheckBox */
QCheckBox::indicator {
    border: 3px solid rgb(52, 59, 72);
	width: 15px;
	height: 15px;
	border-radius: 10px;
    background: rgb(44, 49, 60);
}
QCheckBox::indicator:hover {
    border: 3px solid rgb(58, 66, 81);
}
QCheckBox::indicator:checked {
    background: 3px solid rgb(52, 59, 72);
	border: 3px solid rgb(52, 59, 72);	
	background-image: url(:/icons/images/icons/cil-check-alt.png);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
RadioButton */
QRadioButton::indicator {
    border: 3px solid rgb(52, 59, 72);
	width: 15px;
	height: 15px;
	border-radius: 10px;
    background: rgb(44, 49, 60);
}
QRadioButton::indicator:hover {
    border: 3px solid rgb(58, 66, 81);
}
QRadioButton::indicator:checked {
    background: 3px solid rgb(94, 106, 130);
	border: 3px solid rgb(52, 59, 72);	
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
ComboBox */
QComboBox{
	background-color: rgb(27, 29, 35);
	border-radius: 5px;
	border: 2px solid rgb(33, 37, 43);
	padding: 5px;
	padding-left: 10px;
}
QComboBox:hover{
	border: 2px solid rgb(64, 71, 88);
}
QComboBox::drop-down {
	subcontrol-origin: padding;
	subcontrol-position: top right;
	width: 25px; 
	border-left-width: 3px;
	border-left-color: rgba(39, 44, 54, 150);
	border-left-style: solid;
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;	
	background-image: url(:/icons/images/icons/cil-arrow-bottom.png);
	background-position: center;
	background-repeat: no-reperat;
 }
QComboBox QAbstractItemView {
	color: rgb(255, 121, 198);	
	background-color: rgb(33, 37, 43);
	padding: 10px;
	selection-background-color: rgb(39, 44, 54);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Sliders */
QSlider::groove:horizontal {
    border-radius: 5px;
    height: 10px;
	margin: 0px;
	background-color: rgb(52, 59, 72);
}
QSlider::groove:horizontal:hover {
	background-color: rgb(55, 62, 76);
}
QSlider::handle:horizontal {
    background-color: rgb(189, 147, 249);
    border: none;
    height: 10px;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}
QSlider::handle:horizontal:hover {
    background-color: rgb(195, 155, 255);
}
QSlider::handle:horizontal:pressed {
    background-color: rgb(255, 121, 198);
}

QSlider::groove:vertical {
    border-radius: 5px;
    width: 10px;
    margin: 0px;
	background-color: rgb(52, 59, 72);
}
QSlider::groove:vertical:hover {
	background-color: rgb(55, 62, 76);
}
QSlider::handle:vertical {
    background-color: rgb(189, 147, 249);
	border: none;
    height: 10px;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}
QSlider::handle:vertical:hover {
    background-color: rgb(195, 155, 255);
}
QSlider::handle:vertical:pressed {
    background-color: rgb(255, 121, 198);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
CommandLinkButton */
QCommandLinkButton {	
	color: rgb(255, 121, 198);
	border-radius: 5px;
	padding: 5px;
	color: rgb(255, 170, 255);
}
QCommandLinkButton:hover {	
	color: rgb(255, 170, 255);
	background-color: rgb(44, 49, 60);
}
QCommandLinkButton:pressed {	
	color: rgb(189, 147, 249);
	background-color: rgb(52, 58, 71);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Button */
#pagesContainer QPushButton {
	border: 2px solid rgb(52, 59, 72);
	border-radius: 5px;	
	background-color: rgb(52, 59, 72);
}
#pagesContainer QPushButton:hover {
	background-color: rgb(57, 65, 80);
	border: 2px solid rgb(61, 70, 86);
}
#pagesContainer QPushButton:pressed {	
	background-color: rgb(35, 40, 49);
	border: 2px solid rgb(43, 50, 61);
}

</string>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_12">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>10</number>
    </property>
    <property name="topMargin">
     <number>10</number>
    </property>
    <property name="rightMargin">
     <number>10</number>
    </property>
    <property name="bottomMargin">
     <number>10</number>
    </property>
    <item>
     <widget class="QFrame" name="bgApp">
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QHBoxLayout" name="appLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QFrame" name="leftMenuBg">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>60</width>
           <height>********</height>
          </size>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QFrame" name="topLogoInfo">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>50</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>********</width>
              <height>50</height>
             </size>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <widget class="QFrame" name="topLogo">
             <property name="geometry">
              <rect>
               <x>10</x>
               <y>5</y>
               <width>42</width>
               <height>42</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>42</width>
               <height>42</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>42</width>
               <height>42</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">border-image: url(:/images/images/images/ui图标.png);
border-radius: 21px;</string>
             </property>
             <property name="frameShape">
              <enum>QFrame::NoFrame</enum>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Raised</enum>
             </property>
            </widget>
            <widget class="QLabel" name="titleLeftApp">
             <property name="geometry">
              <rect>
               <x>70</x>
               <y>8</y>
               <width>160</width>
               <height>20</height>
              </rect>
             </property>
             <property name="font">
              <font>
               <family>Segoe UI Semibold</family>
               <pointsize>12</pointsize>
               <weight>7</weight>
               <italic>false</italic>
               <bold>false</bold>
              </font>
             </property>
             <property name="text">
              <string>软件及整机</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
             </property>
            </widget>
            <widget class="QLabel" name="titleLeftDescription">
             <property name="geometry">
              <rect>
               <x>70</x>
               <y>27</y>
               <width>160</width>
               <height>16</height>
              </rect>
             </property>
             <property name="maximumSize">
              <size>
               <width>********</width>
               <height>16</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Segoe UI</family>
               <pointsize>8</pointsize>
               <weight>50</weight>
               <italic>false</italic>
               <bold>false</bold>
              </font>
             </property>
             <property name="text">
              <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;R&amp;amp;DC-SCG-TOOL&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
             </property>
            </widget>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="leftMenuFrame">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalMenuLayout">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QFrame" name="toggleBox">
               <property name="maximumSize">
                <size>
                 <width>********</width>
                 <height>45</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_4">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QPushButton" name="toggleButton">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>11</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/icon_menu.png);</string>
                  </property>
                  <property name="text">
                   <string>Hide</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item alignment="Qt::AlignTop">
              <widget class="QFrame" name="topMenu">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_8">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QPushButton" name="btn_home">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>11</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-home.png);</string>
                  </property>
                  <property name="text">
                   <string>主要功能</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_widgets">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>11</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">
background-image: url(:/icons/images/icons/cil-chart.png);</string>
                  </property>
                  <property name="text">
                   <string>数据图表</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_ota">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>11</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-arrow-circle-top.png);</string>
                  </property>
                  <property name="text">
                   <string>OTA</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_document">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>11</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-file.png);</string>
                  </property>
                  <property name="text">
                   <string>文档</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_exit">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>11</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">
background-image: url(:/icons/images/icons/cil-loop-circular.png);</string>
                  </property>
                  <property name="text">
                   <string>style</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item alignment="Qt::AlignBottom">
              <widget class="QFrame" name="bottomMenu">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_9">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QPushButton" name="toggleLeftBox">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>11</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/icon_settings.png);</string>
                  </property>
                  <property name="text">
                   <string>Left Box</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="extraLeftBox">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>0</width>
           <height>********</height>
          </size>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="extraColumLayout">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QFrame" name="extraTopBg">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>50</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>********</width>
              <height>50</height>
             </size>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_5">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <layout class="QGridLayout" name="extraTopLayout">
               <property name="leftMargin">
                <number>10</number>
               </property>
               <property name="rightMargin">
                <number>10</number>
               </property>
               <property name="horizontalSpacing">
                <number>10</number>
               </property>
               <property name="verticalSpacing">
                <number>0</number>
               </property>
               <item row="0" column="0">
                <widget class="QFrame" name="extraIcon">
                 <property name="minimumSize">
                  <size>
                   <width>20</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>20</width>
                   <height>20</height>
                  </size>
                 </property>
                 <property name="frameShape">
                  <enum>QFrame::NoFrame</enum>
                 </property>
                 <property name="frameShadow">
                  <enum>QFrame::Raised</enum>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLabel" name="extraLabel">
                 <property name="minimumSize">
                  <size>
                   <width>150</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="text">
                  <string>Left Box</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="2">
                <widget class="QPushButton" name="extraCloseColumnBtn">
                 <property name="minimumSize">
                  <size>
                   <width>28</width>
                   <height>28</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>28</width>
                   <height>28</height>
                  </size>
                 </property>
                 <property name="cursor">
                  <cursorShape>PointingHandCursor</cursorShape>
                 </property>
                 <property name="toolTip">
                  <string>Close left box</string>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                 <property name="icon">
                  <iconset resource="resources.qrc">
                   <normaloff>:/icons/images/icons/icon_close.png</normaloff>:/icons/images/icons/icon_close.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>20</width>
                   <height>20</height>
                  </size>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="extraContent">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_12">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item alignment="Qt::AlignTop">
              <widget class="QFrame" name="extraTopMenu">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_11">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QPushButton" name="btn_share">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>11</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-share-boxed.png);</string>
                  </property>
                  <property name="text">
                   <string>Share</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_adjustments">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>11</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-equalizer.png);</string>
                  </property>
                  <property name="text">
                   <string>Adjustments</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_more">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>11</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-image: url(:/icons/images/icons/cil-layers.png);</string>
                  </property>
                  <property name="text">
                   <string>More</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="extraCenter">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_10">
                <item>
                 <widget class="QTextEdit" name="textEdit">
                  <property name="minimumSize">
                   <size>
                    <width>222</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background: transparent;</string>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                  <property name="html">
                   <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'微软雅黑'; font-size:11pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'Segoe UI'; font-size:12pt; color:#ff00ff;&quot;&gt;工具适用范围&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'Segoe UI'; font-size:10pt;&quot;&gt;适用于软件及整机测试&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'Segoe UI'; font-size:10pt;&quot;&gt;处理充电时长、筛选数据&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'Segoe UI'; font-size:10pt;&quot;&gt;筛选并生成曲线图&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'Segoe UI'; font-size:12pt; color:#ff55ff;&quot;&gt;其他功能&lt;/span&gt;&lt;/p&gt;
&lt;p align=&quot;center&quot; style=&quot; margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'Segoe UI'; font-size:9pt; color:#ff55ff;&quot;&gt;By：An--可以新增功能&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="extraBottom">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="contentBox">
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QFrame" name="contentTopBg">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>50</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>********</width>
              <height>50</height>
             </size>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QFrame" name="leftBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_3">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="titleRightInfo">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>********</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>11</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>RDC-研发部-测试组-数据处理工具</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item alignment="Qt::AlignRight">
              <widget class="QFrame" name="rightButtons">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>28</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_2">
                <property name="spacing">
                 <number>5</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QPushButton" name="settingsTopBtn">
                  <property name="minimumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="toolTip">
                   <string>Settings</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="resources.qrc">
                    <normaloff>:/icons/images/icons/icon_settings.png</normaloff>:/icons/images/icons/icon_settings.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="minimizeAppBtn">
                  <property name="minimumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="toolTip">
                   <string>Minimize</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="resources.qrc">
                    <normaloff>:/icons/images/icons/icon_minimize.png</normaloff>:/icons/images/icons/icon_minimize.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="maximizeRestoreAppBtn">
                  <property name="minimumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>11</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                    <stylestrategy>PreferDefault</stylestrategy>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="toolTip">
                   <string>Maximize</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="resources.qrc">
                    <normaloff>:/icons/images/icons/icon_maximize.png</normaloff>:/icons/images/icons/icon_maximize.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="closeAppBtn">
                  <property name="minimumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>28</width>
                    <height>28</height>
                   </size>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="toolTip">
                   <string>Close</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="resources.qrc">
                    <normaloff>:/icons/images/icons/icon_close.png</normaloff>:/icons/images/icons/icon_close.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="contentBottom">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_6">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QFrame" name="content">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_4">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QFrame" name="pagesContainer">
                  <property name="styleSheet">
                   <string notr="true"/>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_15">
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <property name="leftMargin">
                    <number>10</number>
                   </property>
                   <property name="topMargin">
                    <number>10</number>
                   </property>
                   <property name="rightMargin">
                    <number>10</number>
                   </property>
                   <property name="bottomMargin">
                    <number>10</number>
                   </property>
                   <item>
                    <widget class="QStackedWidget" name="stackedWidget">
                     <property name="styleSheet">
                      <string notr="true">background: transparent;</string>
                     </property>
                     <property name="currentIndex">
                      <number>2</number>
                     </property>
                     <widget class="QWidget" name="home">
                      <property name="styleSheet">
                       <string notr="true"/>
                      </property>
                      <layout class="QHBoxLayout" name="horizontalLayout_6">
                       <item>
                        <widget class="QTextEdit" name="textEdit_2">
                         <property name="sizePolicy">
                          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                           <horstretch>0</horstretch>
                           <verstretch>0</verstretch>
                          </sizepolicy>
                         </property>
                         <property name="minimumSize">
                          <size>
                           <width>500</width>
                           <height>700</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>600</width>
                           <height>********</height>
                          </size>
                         </property>
                         <property name="font">
                          <font>
                           <family>微软雅黑</family>
                           <pointsize>11</pointsize>
                           <weight>50</weight>
                           <italic>false</italic>
                           <bold>false</bold>
                          </font>
                         </property>
                         <property name="styleSheet">
                          <string notr="true"/>
                         </property>
                         <property name="placeholderText">
                          <string>数据展示</string>
                         </property>
                        </widget>
                       </item>
                       <item>
                        <widget class="QFrame" name="frame_6">
                         <property name="minimumSize">
                          <size>
                           <width>640</width>
                           <height>700</height>
                          </size>
                         </property>
                         <property name="styleSheet">
                          <string notr="true">QFrame#frame_6{
	border: 1px solid #343b48;
	/*background-color: rgb(126, 199, 193);*/
	border-radius:20px;

}
QFrame#frame_7{
	/*background-color: rgb(161, 217, 218);*/
	border-radius:20px;

}
QFrame#frame_8{
	/*background-color: rgb(161, 217, 218);*/
	border-radius:20px;

}
QPushButton {
	border-radius:20px;
	/*background-color: rgb(255, 255, 255);*/
}

QPushButton:hover {
	color: black;
	background-color: rgb(207, 232, 232);
	padding-left: 15px;
}
QPushButton:pressed {
	/*color: white;*/
	background-color: rgb(189, 147, 249);
	border-style: inset;
	padding-left: 15px;
}
QLineEdit{
	/*color: rgb(0, 0, 0);*/
	background-color: rgb(255, 255, 255);
	border-radius:15px;
}
QLabel#info_text{
	font-size:22px;
}</string>
                         </property>
                         <property name="frameShape">
                          <enum>QFrame::StyledPanel</enum>
                         </property>
                         <property name="frameShadow">
                          <enum>QFrame::Raised</enum>
                         </property>
                         <layout class="QVBoxLayout" name="verticalLayout_21">
                          <property name="leftMargin">
                           <number>5</number>
                          </property>
                          <property name="topMargin">
                           <number>5</number>
                          </property>
                          <property name="rightMargin">
                           <number>7</number>
                          </property>
                          <property name="bottomMargin">
                           <number>5</number>
                          </property>
                          <item>
                           <widget class="QFrame" name="frame_7">
                            <property name="sizePolicy">
                             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                              <horstretch>0</horstretch>
                              <verstretch>0</verstretch>
                             </sizepolicy>
                            </property>
                            <property name="styleSheet">
                             <string notr="true"/>
                            </property>
                            <property name="frameShape">
                             <enum>QFrame::StyledPanel</enum>
                            </property>
                            <property name="frameShadow">
                             <enum>QFrame::Raised</enum>
                            </property>
                            <layout class="QVBoxLayout" name="verticalLayout_22">
                             <item>
                              <layout class="QHBoxLayout" name="horizontalLayout_8">
                               <item>
                                <spacer name="horizontalSpacer_5">
                                 <property name="orientation">
                                  <enum>Qt::Horizontal</enum>
                                 </property>
                                 <property name="sizeType">
                                  <enum>QSizePolicy::Minimum</enum>
                                 </property>
                                 <property name="sizeHint" stdset="0">
                                  <size>
                                   <width>230</width>
                                   <height>20</height>
                                  </size>
                                 </property>
                                </spacer>
                               </item>
                               <item>
                                <widget class="QFrame" name="frame_9">
                                 <property name="sizePolicy">
                                  <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                                   <horstretch>0</horstretch>
                                   <verstretch>0</verstretch>
                                  </sizepolicy>
                                 </property>
                                 <property name="minimumSize">
                                  <size>
                                   <width>100</width>
                                   <height>40</height>
                                  </size>
                                 </property>
                                 <property name="maximumSize">
                                  <size>
                                   <width>100</width>
                                   <height>40</height>
                                  </size>
                                 </property>
                                 <property name="frameShape">
                                  <enum>QFrame::StyledPanel</enum>
                                 </property>
                                 <property name="frameShadow">
                                  <enum>QFrame::Raised</enum>
                                 </property>
                                 <layout class="QHBoxLayout" name="horizontalLayout_7">
                                  <property name="leftMargin">
                                   <number>20</number>
                                  </property>
                                  <property name="topMargin">
                                   <number>0</number>
                                  </property>
                                  <property name="rightMargin">
                                   <number>30</number>
                                  </property>
                                  <property name="bottomMargin">
                                   <number>0</number>
                                  </property>
                                  <item>
                                   <widget class="QLabel" name="label_15">
                                    <property name="minimumSize">
                                     <size>
                                      <width>40</width>
                                      <height>40</height>
                                     </size>
                                    </property>
                                    <property name="font">
                                     <font>
                                      <family>微软雅黑</family>
                                      <pointsize>11</pointsize>
                                      <weight>50</weight>
                                      <italic>false</italic>
                                      <bold>false</bold>
                                     </font>
                                    </property>
                                    <property name="styleSheet">
                                     <string notr="true">image: url(:/icons/images/icons/cil-wallet.png);</string>
                                    </property>
                                    <property name="text">
                                     <string/>
                                    </property>
                                    <property name="alignment">
                                     <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                                    </property>
                                   </widget>
                                  </item>
                                  <item>
                                   <widget class="QLabel" name="info_text">
                                    <property name="minimumSize">
                                     <size>
                                      <width>47</width>
                                      <height>35</height>
                                     </size>
                                    </property>
                                    <property name="font">
                                     <font>
                                      <family>微软雅黑</family>
                                      <pointsize>-1</pointsize>
                                      <weight>50</weight>
                                      <italic>false</italic>
                                      <bold>false</bold>
                                     </font>
                                    </property>
                                    <property name="text">
                                     <string>Info</string>
                                    </property>
                                    <property name="alignment">
                                     <set>Qt::AlignCenter</set>
                                    </property>
                                   </widget>
                                  </item>
                                 </layout>
                                </widget>
                               </item>
                               <item>
                                <spacer name="horizontalSpacer_6">
                                 <property name="orientation">
                                  <enum>Qt::Horizontal</enum>
                                 </property>
                                 <property name="sizeType">
                                  <enum>QSizePolicy::Minimum</enum>
                                 </property>
                                 <property name="sizeHint" stdset="0">
                                  <size>
                                   <width>230</width>
                                   <height>20</height>
                                  </size>
                                 </property>
                                </spacer>
                               </item>
                              </layout>
                             </item>
                             <item>
                              <layout class="QHBoxLayout" name="horizontalLayout_16">
                               <property name="spacing">
                                <number>8</number>
                               </property>
                               <property name="leftMargin">
                                <number>8</number>
                               </property>
                               <property name="topMargin">
                                <number>0</number>
                               </property>
                               <property name="rightMargin">
                                <number>8</number>
                               </property>
                               <property name="bottomMargin">
                                <number>0</number>
                               </property>
                               <item>
                                <layout class="QVBoxLayout" name="verticalLayout_23">
                                 <property name="spacing">
                                  <number>12</number>
                                 </property>
                                 <property name="leftMargin">
                                  <number>12</number>
                                 </property>
                                 <property name="topMargin">
                                  <number>0</number>
                                 </property>
                                 <property name="rightMargin">
                                  <number>12</number>
                                 </property>
                                 <property name="bottomMargin">
                                  <number>0</number>
                                 </property>
                                 <item>
                                  <layout class="QHBoxLayout" name="horizontalLayout_10">
                                   <property name="topMargin">
                                    <number>5</number>
                                   </property>
                                   <property name="bottomMargin">
                                    <number>5</number>
                                   </property>
                                   <item>
                                    <widget class="QLabel" name="label_10">
                                     <property name="minimumSize">
                                      <size>
                                       <width>85</width>
                                       <height>30</height>
                                      </size>
                                     </property>
                                     <property name="font">
                                      <font>
                                       <family>微软雅黑</family>
                                       <pointsize>11</pointsize>
                                       <weight>50</weight>
                                       <italic>false</italic>
                                       <bold>false</bold>
                                      </font>
                                     </property>
                                     <property name="text">
                                      <string>保存 路径</string>
                                     </property>
                                     <property name="alignment">
                                      <set>Qt::AlignCenter</set>
                                     </property>
                                    </widget>
                                   </item>
                                   <item>
                                    <widget class="QLineEdit" name="line_filename">
                                     <property name="minimumSize">
                                      <size>
                                       <width>284</width>
                                       <height>40</height>
                                      </size>
                                     </property>
                                     <property name="font">
                                      <font>
                                       <family>微软雅黑</family>
                                       <pointsize>11</pointsize>
                                       <weight>50</weight>
                                       <italic>false</italic>
                                       <bold>false</bold>
                                      </font>
                                     </property>
                                     <property name="toolTip">
                                      <string>多个路径的情况下注意重新命名文件</string>
                                     </property>
                                     <property name="styleSheet">
                                      <string notr="true"/>
                                     </property>
                                     <property name="placeholderText">
                                      <string>默认工具放置路径下的data.csv</string>
                                     </property>
                                    </widget>
                                   </item>
                                  </layout>
                                 </item>
                                 <item>
                                  <layout class="QHBoxLayout" name="horizontalLayout_15">
                                   <property name="topMargin">
                                    <number>5</number>
                                   </property>
                                   <property name="bottomMargin">
                                    <number>5</number>
                                   </property>
                                   <item>
                                    <widget class="QLabel" name="label_11">
                                     <property name="minimumSize">
                                      <size>
                                       <width>85</width>
                                       <height>30</height>
                                      </size>
                                     </property>
                                     <property name="font">
                                      <font>
                                       <family>微软雅黑</family>
                                       <pointsize>11</pointsize>
                                       <weight>50</weight>
                                       <italic>false</italic>
                                       <bold>false</bold>
                                      </font>
                                     </property>
                                     <property name="text">
                                      <string>文件 路径</string>
                                     </property>
                                     <property name="alignment">
                                      <set>Qt::AlignCenter</set>
                                     </property>
                                    </widget>
                                   </item>
                                   <item>
                                    <widget class="QLineEdit" name="line_filepath">
                                     <property name="minimumSize">
                                      <size>
                                       <width>284</width>
                                       <height>40</height>
                                      </size>
                                     </property>
                                     <property name="font">
                                      <font>
                                       <family>微软雅黑</family>
                                       <pointsize>11</pointsize>
                                       <weight>50</weight>
                                       <italic>false</italic>
                                       <bold>false</bold>
                                      </font>
                                     </property>
                                     <property name="styleSheet">
                                      <string notr="true"/>
                                     </property>
                                     <property name="placeholderText">
                                      <string>需要打开的数据文件</string>
                                     </property>
                                    </widget>
                                   </item>
                                  </layout>
                                 </item>
                                 <item>
                                  <layout class="QHBoxLayout" name="horizontalLayout_17">
                                   <property name="topMargin">
                                    <number>5</number>
                                   </property>
                                   <property name="bottomMargin">
                                    <number>5</number>
                                   </property>
                                   <item>
                                    <widget class="QLabel" name="label_12">
                                     <property name="minimumSize">
                                      <size>
                                       <width>85</width>
                                       <height>30</height>
                                      </size>
                                     </property>
                                     <property name="font">
                                      <font>
                                       <family>微软雅黑</family>
                                       <pointsize>11</pointsize>
                                       <weight>50</weight>
                                       <italic>false</italic>
                                       <bold>false</bold>
                                      </font>
                                     </property>
                                     <property name="text">
                                      <string>数据条件1</string>
                                     </property>
                                     <property name="alignment">
                                      <set>Qt::AlignCenter</set>
                                     </property>
                                    </widget>
                                   </item>
                                   <item>
                                    <widget class="QLineEdit" name="line_data1">
                                     <property name="minimumSize">
                                      <size>
                                       <width>284</width>
                                       <height>40</height>
                                      </size>
                                     </property>
                                     <property name="font">
                                      <font>
                                       <family>微软雅黑</family>
                                       <pointsize>11</pointsize>
                                       <weight>50</weight>
                                       <italic>false</italic>
                                       <bold>false</bold>
                                      </font>
                                     </property>
                                     <property name="toolTip">
                                      <string>注意不要&lt;&gt;</string>
                                     </property>
                                     <property name="styleSheet">
                                      <string notr="true"/>
                                     </property>
                                     <property name="placeholderText">
                                      <string>motor</string>
                                     </property>
                                    </widget>
                                   </item>
                                  </layout>
                                 </item>
                                 <item>
                                  <layout class="QHBoxLayout" name="horizontalLayout_18">
                                   <property name="topMargin">
                                    <number>5</number>
                                   </property>
                                   <property name="bottomMargin">
                                    <number>5</number>
                                   </property>
                                   <item>
                                    <widget class="QLabel" name="label_13">
                                     <property name="minimumSize">
                                      <size>
                                       <width>85</width>
                                       <height>30</height>
                                      </size>
                                     </property>
                                     <property name="font">
                                      <font>
                                       <family>微软雅黑</family>
                                       <pointsize>11</pointsize>
                                       <weight>50</weight>
                                       <italic>false</italic>
                                       <bold>false</bold>
                                      </font>
                                     </property>
                                     <property name="text">
                                      <string>数据条件2</string>
                                     </property>
                                     <property name="alignment">
                                      <set>Qt::AlignCenter</set>
                                     </property>
                                    </widget>
                                   </item>
                                   <item>
                                    <widget class="QLineEdit" name="line_data2">
                                     <property name="minimumSize">
                                      <size>
                                       <width>284</width>
                                       <height>40</height>
                                      </size>
                                     </property>
                                     <property name="font">
                                      <font>
                                       <family>微软雅黑</family>
                                       <pointsize>11</pointsize>
                                       <weight>50</weight>
                                       <italic>false</italic>
                                       <bold>false</bold>
                                      </font>
                                     </property>
                                     <property name="toolTip">
                                      <string>注意不要&lt;&gt;</string>
                                     </property>
                                     <property name="statusTip">
                                      <string/>
                                     </property>
                                     <property name="styleSheet">
                                      <string notr="true"/>
                                     </property>
                                     <property name="placeholderText">
                                      <string>imu</string>
                                     </property>
                                    </widget>
                                   </item>
                                  </layout>
                                 </item>
                                 <item>
                                  <layout class="QHBoxLayout" name="horizontalLayout_19">
                                   <property name="topMargin">
                                    <number>5</number>
                                   </property>
                                   <property name="bottomMargin">
                                    <number>5</number>
                                   </property>
                                   <item>
                                    <widget class="QPushButton" name="btn_get">
                                     <property name="minimumSize">
                                      <size>
                                       <width>200</width>
                                       <height>45</height>
                                      </size>
                                     </property>
                                     <property name="font">
                                      <font>
                                       <family>微软雅黑</family>
                                       <pointsize>11</pointsize>
                                       <weight>50</weight>
                                       <italic>false</italic>
                                       <bold>false</bold>
                                      </font>
                                     </property>
                                     <property name="cursor">
                                      <cursorShape>PointingHandCursor</cursorShape>
                                     </property>
                                     <property name="styleSheet">
                                      <string notr="true"/>
                                     </property>
                                     <property name="text">
                                      <string>获取文件路径</string>
                                     </property>
                                    </widget>
                                   </item>
                                   <item>
                                    <widget class="QPushButton" name="btn_get_all_data">
                                     <property name="minimumSize">
                                      <size>
                                       <width>200</width>
                                       <height>45</height>
                                      </size>
                                     </property>
                                     <property name="font">
                                      <font>
                                       <family>微软雅黑</family>
                                       <pointsize>11</pointsize>
                                       <weight>50</weight>
                                       <italic>false</italic>
                                       <bold>false</bold>
                                      </font>
                                     </property>
                                     <property name="cursor">
                                      <cursorShape>PointingHandCursor</cursorShape>
                                     </property>
                                     <property name="styleSheet">
                                      <string notr="true"/>
                                     </property>
                                     <property name="text">
                                      <string>获取数据</string>
                                     </property>
                                    </widget>
                                   </item>
                                  </layout>
                                 </item>
                                </layout>
                               </item>
                              </layout>
                             </item>
                            </layout>
                           </widget>
                          </item>
                          <item>
                           <widget class="QFrame" name="frame_8">
                            <property name="font">
                             <font>
                              <family>微软雅黑</family>
                              <pointsize>11</pointsize>
                              <weight>50</weight>
                              <italic>false</italic>
                              <bold>false</bold>
                             </font>
                            </property>
                            <property name="styleSheet">
                             <string notr="true"/>
                            </property>
                            <property name="frameShape">
                             <enum>QFrame::StyledPanel</enum>
                            </property>
                            <property name="frameShadow">
                             <enum>QFrame::Raised</enum>
                            </property>
                            <layout class="QHBoxLayout" name="horizontalLayout_21">
                             <item>
                              <layout class="QVBoxLayout" name="verticalLayout_24">
                               <item>
                                <layout class="QHBoxLayout" name="horizontalLayout_20">
                                 <item>
                                  <spacer name="horizontalSpacer_9">
                                   <property name="orientation">
                                    <enum>Qt::Horizontal</enum>
                                   </property>
                                   <property name="sizeHint" stdset="0">
                                    <size>
                                     <width>170</width>
                                     <height>20</height>
                                    </size>
                                   </property>
                                  </spacer>
                                 </item>
                                 <item>
                                  <widget class="QFrame" name="frame_10">
                                   <property name="minimumSize">
                                    <size>
                                     <width>200</width>
                                     <height>45</height>
                                    </size>
                                   </property>
                                   <property name="maximumSize">
                                    <size>
                                     <width>********</width>
                                     <height>45</height>
                                    </size>
                                   </property>
                                   <property name="frameShape">
                                    <enum>QFrame::StyledPanel</enum>
                                   </property>
                                   <property name="frameShadow">
                                    <enum>QFrame::Raised</enum>
                                   </property>
                                   <widget class="QLabel" name="label_16">
                                    <property name="geometry">
                                     <rect>
                                      <x>10</x>
                                      <y>10</y>
                                      <width>180</width>
                                      <height>30</height>
                                     </rect>
                                    </property>
                                    <property name="font">
                                     <font>
                                      <family>微软雅黑</family>
                                      <pointsize>11</pointsize>
                                      <weight>50</weight>
                                      <italic>false</italic>
                                      <bold>false</bold>
                                     </font>
                                    </property>
                                    <property name="text">
                                     <string>其他功能参照上述筛查</string>
                                    </property>
                                    <property name="alignment">
                                     <set>Qt::AlignCenter</set>
                                    </property>
                                   </widget>
                                  </widget>
                                 </item>
                                 <item>
                                  <spacer name="horizontalSpacer_10">
                                   <property name="orientation">
                                    <enum>Qt::Horizontal</enum>
                                   </property>
                                   <property name="sizeHint" stdset="0">
                                    <size>
                                     <width>170</width>
                                     <height>20</height>
                                    </size>
                                   </property>
                                  </spacer>
                                 </item>
                                </layout>
                               </item>
                               <item>
                                <layout class="QHBoxLayout" name="horizontalLayout_23">
                                 <property name="leftMargin">
                                  <number>5</number>
                                 </property>
                                 <property name="topMargin">
                                  <number>5</number>
                                 </property>
                                 <property name="rightMargin">
                                  <number>5</number>
                                 </property>
                                 <property name="bottomMargin">
                                  <number>5</number>
                                 </property>
                                 <item>
                                  <widget class="QPushButton" name="btn_chang_jump">
                                   <property name="minimumSize">
                                    <size>
                                     <width>140</width>
                                     <height>45</height>
                                    </size>
                                   </property>
                                   <property name="font">
                                    <font>
                                     <family>微软雅黑</family>
                                     <pointsize>11</pointsize>
                                     <weight>50</weight>
                                     <italic>false</italic>
                                     <bold>false</bold>
                                    </font>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <property name="toolTip">
                                    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;info的跳电情况&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                                   </property>
                                   <property name="text">
                                    <string>Info电量</string>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QPushButton" name="btn_bat_jump">
                                   <property name="minimumSize">
                                    <size>
                                     <width>140</width>
                                     <height>45</height>
                                    </size>
                                   </property>
                                   <property name="font">
                                    <font>
                                     <family>微软雅黑</family>
                                     <pointsize>11</pointsize>
                                     <weight>50</weight>
                                     <italic>false</italic>
                                     <bold>false</bold>
                                    </font>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <property name="toolTip">
                                    <string>bat命令电量跳电情况</string>
                                   </property>
                                   <property name="text">
                                    <string>bat电量</string>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QPushButton" name="btn_time">
                                   <property name="minimumSize">
                                    <size>
                                     <width>140</width>
                                     <height>45</height>
                                    </size>
                                   </property>
                                   <property name="font">
                                    <font>
                                     <family>微软雅黑</family>
                                     <pointsize>11</pointsize>
                                     <weight>50</weight>
                                     <italic>false</italic>
                                     <bold>false</bold>
                                    </font>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <property name="toolTip">
                                    <string>info充放电时长</string>
                                   </property>
                                   <property name="text">
                                    <string>充放电时长</string>
                                   </property>
                                  </widget>
                                 </item>
                                </layout>
                               </item>
                               <item>
                                <layout class="QHBoxLayout" name="horizontalLayout_24">
                                 <property name="leftMargin">
                                  <number>5</number>
                                 </property>
                                 <property name="topMargin">
                                  <number>5</number>
                                 </property>
                                 <property name="rightMargin">
                                  <number>5</number>
                                 </property>
                                 <property name="bottomMargin">
                                  <number>5</number>
                                 </property>
                                 <item>
                                  <widget class="QPushButton" name="btn_error">
                                   <property name="minimumSize">
                                    <size>
                                     <width>140</width>
                                     <height>45</height>
                                    </size>
                                   </property>
                                   <property name="font">
                                    <font>
                                     <family>微软雅黑</family>
                                     <pointsize>11</pointsize>
                                     <weight>50</weight>
                                     <italic>false</italic>
                                     <bold>false</bold>
                                    </font>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <property name="toolTip">
                                    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;info告警&lt;br/&gt;wip、wop等&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                                   </property>
                                   <property name="text">
                                    <string>检查告警</string>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QPushButton" name="btn_status">
                                   <property name="minimumSize">
                                    <size>
                                     <width>140</width>
                                     <height>45</height>
                                    </size>
                                   </property>
                                   <property name="font">
                                    <font>
                                     <family>微软雅黑</family>
                                     <pointsize>11</pointsize>
                                     <weight>50</weight>
                                     <italic>false</italic>
                                     <bold>false</bold>
                                    </font>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <property name="toolTip">
                                    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;充电状态检测&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                                   </property>
                                   <property name="text">
                                    <string>充电状态</string>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QPushButton" name="btn_dazhaun">
                                   <property name="minimumSize">
                                    <size>
                                     <width>140</width>
                                     <height>45</height>
                                    </size>
                                   </property>
                                   <property name="font">
                                    <font>
                                     <family>微软雅黑</family>
                                     <pointsize>11</pointsize>
                                     <weight>50</weight>
                                     <italic>false</italic>
                                     <bold>false</bold>
                                    </font>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <property name="text">
                                    <string>路径打转</string>
                                   </property>
                                  </widget>
                                 </item>
                                </layout>
                               </item>
                               <item>
                                <layout class="QHBoxLayout" name="horizontalLayout_25">
                                 <property name="leftMargin">
                                  <number>5</number>
                                 </property>
                                 <property name="topMargin">
                                  <number>5</number>
                                 </property>
                                 <property name="rightMargin">
                                  <number>5</number>
                                 </property>
                                 <property name="bottomMargin">
                                  <number>5</number>
                                 </property>
                                 <item>
                                  <widget class="QPushButton" name="start_vol_cur">
                                   <property name="minimumSize">
                                    <size>
                                     <width>140</width>
                                     <height>45</height>
                                    </size>
                                   </property>
                                   <property name="font">
                                    <font>
                                     <family>微软雅黑</family>
                                     <pointsize>11</pointsize>
                                     <weight>50</weight>
                                     <italic>false</italic>
                                     <bold>false</bold>
                                    </font>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <property name="text">
                                    <string>Start-Ent参数</string>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QPushButton" name="pushButton_8">
                                   <property name="minimumSize">
                                    <size>
                                     <width>140</width>
                                     <height>45</height>
                                    </size>
                                   </property>
                                   <property name="font">
                                    <font>
                                     <family>微软雅黑</family>
                                     <pointsize>11</pointsize>
                                     <weight>50</weight>
                                     <italic>false</italic>
                                     <bold>false</bold>
                                    </font>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <property name="text">
                                    <string>电压稳定性</string>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QPushButton" name="pushButton_10">
                                   <property name="minimumSize">
                                    <size>
                                     <width>140</width>
                                     <height>45</height>
                                    </size>
                                   </property>
                                   <property name="font">
                                    <font>
                                     <family>微软雅黑</family>
                                     <pointsize>11</pointsize>
                                     <weight>50</weight>
                                     <italic>false</italic>
                                     <bold>false</bold>
                                    </font>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <property name="text">
                                    <string>电流稳定性</string>
                                   </property>
                                  </widget>
                                 </item>
                                </layout>
                               </item>
                              </layout>
                             </item>
                            </layout>
                           </widget>
                          </item>
                         </layout>
                        </widget>
                       </item>
                      </layout>
                     </widget>
                     <widget class="QWidget" name="widgets">
                      <property name="styleSheet">
                       <string notr="true">b</string>
                      </property>
                      <layout class="QVBoxLayout" name="verticalLayout">
                       <property name="leftMargin">
                        <number>0</number>
                       </property>
                       <property name="topMargin">
                        <number>0</number>
                       </property>
                       <property name="rightMargin">
                        <number>0</number>
                       </property>
                       <property name="bottomMargin">
                        <number>0</number>
                       </property>
                       <item alignment="Qt::AlignTop">
                        <widget class="QFrame" name="row_1">
                         <property name="sizePolicy">
                          <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                           <horstretch>0</horstretch>
                           <verstretch>0</verstretch>
                          </sizepolicy>
                         </property>
                         <property name="frameShape">
                          <enum>QFrame::StyledPanel</enum>
                         </property>
                         <property name="frameShadow">
                          <enum>QFrame::Raised</enum>
                         </property>
                         <layout class="QVBoxLayout" name="verticalLayout_19">
                          <item>
                           <widget class="QFrame" name="frame_content_wid_1">
                            <property name="frameShape">
                             <enum>QFrame::NoFrame</enum>
                            </property>
                            <property name="frameShadow">
                             <enum>QFrame::Raised</enum>
                            </property>
                            <layout class="QHBoxLayout" name="horizontalLayout_9">
                             <property name="leftMargin">
                              <number>0</number>
                             </property>
                             <property name="topMargin">
                              <number>0</number>
                             </property>
                             <property name="rightMargin">
                              <number>0</number>
                             </property>
                             <property name="bottomMargin">
                              <number>0</number>
                             </property>
                             <item>
                              <widget class="QLineEdit" name="lineEdit">
                               <property name="minimumSize">
                                <size>
                                 <width>450</width>
                                 <height>30</height>
                                </size>
                               </property>
                               <property name="styleSheet">
                                <string notr="true">background-color: rgb(33, 37, 43);</string>
                               </property>
                               <property name="text">
                                <string/>
                               </property>
                               <property name="placeholderText">
                                <string>PATH</string>
                               </property>
                              </widget>
                             </item>
                             <item>
                              <widget class="QPushButton" name="btn_open_csv">
                               <property name="minimumSize">
                                <size>
                                 <width>120</width>
                                 <height>35</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>微软雅黑</family>
                                 <pointsize>11</pointsize>
                                 <weight>50</weight>
                                 <italic>false</italic>
                                 <bold>false</bold>
                                </font>
                               </property>
                               <property name="cursor">
                                <cursorShape>PointingHandCursor</cursorShape>
                               </property>
                               <property name="styleSheet">
                                <string notr="true">background-color: rgb(52, 59, 72);</string>
                               </property>
                               <property name="text">
                                <string>Open</string>
                               </property>
                               <property name="icon">
                                <iconset resource="resources.qrc">
                                 <normaloff>:/icons/images/icons/cil-folder-open.png</normaloff>:/icons/images/icons/cil-folder-open.png</iconset>
                               </property>
                              </widget>
                             </item>
                             <item>
                              <widget class="QComboBox" name="btn_combox_image">
                               <property name="minimumSize">
                                <size>
                                 <width>120</width>
                                 <height>35</height>
                                </size>
                               </property>
                              </widget>
                             </item>
                             <item>
                              <widget class="QComboBox" name="btn_combox_image1">
                               <property name="minimumSize">
                                <size>
                                 <width>120</width>
                                 <height>35</height>
                                </size>
                               </property>
                              </widget>
                             </item>
                             <item>
                              <widget class="QPushButton" name="btn_create_image">
                               <property name="minimumSize">
                                <size>
                                 <width>120</width>
                                 <height>35</height>
                                </size>
                               </property>
                               <property name="font">
                                <font>
                                 <family>微软雅黑</family>
                                 <pointsize>11</pointsize>
                                 <weight>50</weight>
                                 <italic>false</italic>
                                 <bold>false</bold>
                                </font>
                               </property>
                               <property name="cursor">
                                <cursorShape>PointingHandCursor</cursorShape>
                               </property>
                               <property name="styleSheet">
                                <string notr="true">background-color: rgb(52, 59, 72);</string>
                               </property>
                               <property name="text">
                                <string>生成</string>
                               </property>
                              </widget>
                             </item>
                             <item>
                              <widget class="QPushButton" name="btn_clear_image">
                               <property name="minimumSize">
                                <size>
                                 <width>120</width>
                                 <height>35</height>
                                </size>
                               </property>
                               <property name="styleSheet">
                                <string notr="true">background-color: rgb(52, 59, 72);</string>
                               </property>
                               <property name="text">
                                <string>清除</string>
                               </property>
                              </widget>
                             </item>
                            </layout>
                           </widget>
                          </item>
                         </layout>
                        </widget>
                       </item>
                       <item>
                        <widget class="QFrame" name="frame">
                         <property name="sizePolicy">
                          <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                           <horstretch>0</horstretch>
                           <verstretch>0</verstretch>
                          </sizepolicy>
                         </property>
                         <property name="minimumSize">
                          <size>
                           <width>1150</width>
                           <height>0</height>
                          </size>
                         </property>
                         <property name="styleSheet">
                          <string notr="true"/>
                         </property>
                         <property name="frameShape">
                          <enum>QFrame::StyledPanel</enum>
                         </property>
                         <property name="frameShadow">
                          <enum>QFrame::Raised</enum>
                         </property>
                         <layout class="QVBoxLayout" name="verticalLayout_16">
                          <item>
                           <widget class="QWidget" name="widget_2" native="true">
                            <property name="styleSheet">
                             <string notr="true">background-color: rgb(255, 255, 255);</string>
                            </property>
                           </widget>
                          </item>
                         </layout>
                        </widget>
                       </item>
                      </layout>
                     </widget>
                     <widget class="QWidget" name="ota">
                      <layout class="QHBoxLayout" name="horizontalLayout_27">
                       <item>
                        <widget class="QFrame" name="frame_12">
                         <property name="frameShape">
                          <enum>QFrame::StyledPanel</enum>
                         </property>
                         <property name="frameShadow">
                          <enum>QFrame::Raised</enum>
                         </property>
                         <layout class="QHBoxLayout" name="horizontalLayout_29">
                          <property name="leftMargin">
                           <number>0</number>
                          </property>
                          <property name="topMargin">
                           <number>0</number>
                          </property>
                          <property name="rightMargin">
                           <number>0</number>
                          </property>
                          <property name="bottomMargin">
                           <number>0</number>
                          </property>
                          <item>
                           <layout class="QVBoxLayout" name="verticalLayout_34">
                            <item>
                             <layout class="QHBoxLayout" name="horizontalLayout_22">
                              <item>
                               <widget class="QLabel" name="label_8">
                                <property name="minimumSize">
                                 <size>
                                  <width>120</width>
                                  <height>35</height>
                                 </size>
                                </property>
                                <property name="text">
                                 <string>接收端</string>
                                </property>
                                <property name="alignment">
                                 <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <spacer name="horizontalSpacer_3">
                                <property name="orientation">
                                 <enum>Qt::Horizontal</enum>
                                </property>
                                <property name="sizeHint" stdset="0">
                                 <size>
                                  <width>128</width>
                                  <height>20</height>
                                 </size>
                                </property>
                               </spacer>
                              </item>
                              <item>
                               <widget class="QCheckBox" name="hexShowing_checkBox">
                                <property name="sizePolicy">
                                 <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                                  <horstretch>0</horstretch>
                                  <verstretch>0</verstretch>
                                 </sizepolicy>
                                </property>
                                <property name="text">
                                 <string>16进制</string>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QPushButton" name="ClearButton">
                                <property name="minimumSize">
                                 <size>
                                  <width>120</width>
                                  <height>35</height>
                                 </size>
                                </property>
                                <property name="text">
                                 <string>清除</string>
                                </property>
                               </widget>
                              </item>
                             </layout>
                            </item>
                            <item>
                             <widget class="QTextEdit" name="textEdit_Recive">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="minimumSize">
                               <size>
                                <width>450</width>
                                <height>645</height>
                               </size>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">QTextEdit#textEdit_Recive{
	border-radius: 10px;
	border: 1px solid black;
	background-color: rgb(241, 241, 241);
}</string>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </item>
                         </layout>
                        </widget>
                       </item>
                       <item>
                        <widget class="QFrame" name="frame_2">
                         <property name="frameShape">
                          <enum>QFrame::StyledPanel</enum>
                         </property>
                         <property name="frameShadow">
                          <enum>QFrame::Raised</enum>
                         </property>
                         <layout class="QVBoxLayout" name="verticalLayout_30">
                          <property name="leftMargin">
                           <number>0</number>
                          </property>
                          <property name="topMargin">
                           <number>0</number>
                          </property>
                          <property name="rightMargin">
                           <number>0</number>
                          </property>
                          <property name="bottomMargin">
                           <number>0</number>
                          </property>
                          <item>
                           <layout class="QHBoxLayout" name="horizontalLayout_26">
                            <item>
                             <widget class="QFrame" name="frame_5">
                              <property name="minimumSize">
                               <size>
                                <width>275</width>
                                <height>280</height>
                               </size>
                              </property>
                              <property name="frameShape">
                               <enum>QFrame::StyledPanel</enum>
                              </property>
                              <property name="frameShadow">
                               <enum>QFrame::Raised</enum>
                              </property>
                              <layout class="QHBoxLayout" name="horizontalLayout_13">
                               <property name="leftMargin">
                                <number>0</number>
                               </property>
                               <property name="topMargin">
                                <number>0</number>
                               </property>
                               <property name="rightMargin">
                                <number>0</number>
                               </property>
                               <property name="bottomMargin">
                                <number>0</number>
                               </property>
                               <item>
                                <layout class="QVBoxLayout" name="verticalLayout_17">
                                 <item>
                                  <widget class="QLabel" name="Com_Refresh_Label">
                                   <property name="minimumSize">
                                    <size>
                                     <width>120</width>
                                     <height>35</height>
                                    </size>
                                   </property>
                                   <property name="text">
                                    <string>串口搜索</string>
                                   </property>
                                   <property name="alignment">
                                    <set>Qt::AlignCenter</set>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QLabel" name="Com_Baud_Label">
                                   <property name="minimumSize">
                                    <size>
                                     <width>120</width>
                                     <height>35</height>
                                    </size>
                                   </property>
                                   <property name="text">
                                    <string>波特率</string>
                                   </property>
                                   <property name="alignment">
                                    <set>Qt::AlignCenter</set>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QLabel" name="Com_Name_Label">
                                   <property name="minimumSize">
                                    <size>
                                     <width>120</width>
                                     <height>35</height>
                                    </size>
                                   </property>
                                   <property name="text">
                                    <string>串口选择</string>
                                   </property>
                                   <property name="alignment">
                                    <set>Qt::AlignCenter</set>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QLabel" name="Com_Open_Label">
                                   <property name="minimumSize">
                                    <size>
                                     <width>120</width>
                                     <height>35</height>
                                    </size>
                                   </property>
                                   <property name="text">
                                    <string>打开串口</string>
                                   </property>
                                   <property name="alignment">
                                    <set>Qt::AlignCenter</set>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QLabel" name="Com_Close_Label">
                                   <property name="minimumSize">
                                    <size>
                                     <width>120</width>
                                     <height>35</height>
                                    </size>
                                   </property>
                                   <property name="text">
                                    <string>关闭串口</string>
                                   </property>
                                   <property name="alignment">
                                    <set>Qt::AlignCenter</set>
                                   </property>
                                  </widget>
                                 </item>
                                </layout>
                               </item>
                               <item>
                                <layout class="QVBoxLayout" name="verticalLayout_20">
                                 <item>
                                  <widget class="QPushButton" name="Com_Refresh_Button">
                                   <property name="minimumSize">
                                    <size>
                                     <width>120</width>
                                     <height>35</height>
                                    </size>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <property name="text">
                                    <string>刷新</string>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QComboBox" name="Com_Baud_Combo">
                                   <property name="minimumSize">
                                    <size>
                                     <width>120</width>
                                     <height>35</height>
                                    </size>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <item>
                                    <property name="text">
                                     <string>1200</string>
                                    </property>
                                   </item>
                                   <item>
                                    <property name="text">
                                     <string>9600</string>
                                    </property>
                                   </item>
                                   <item>
                                    <property name="text">
                                     <string>14400</string>
                                    </property>
                                   </item>
                                   <item>
                                    <property name="text">
                                     <string>19200</string>
                                    </property>
                                   </item>
                                   <item>
                                    <property name="text">
                                     <string>38400</string>
                                    </property>
                                   </item>
                                   <item>
                                    <property name="text">
                                     <string>57600</string>
                                    </property>
                                   </item>
                                   <item>
                                    <property name="text">
                                     <string>115200</string>
                                    </property>
                                   </item>
                                   <item>
                                    <property name="text">
                                     <string>230400</string>
                                    </property>
                                   </item>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QComboBox" name="Com_Name_Combo">
                                   <property name="minimumSize">
                                    <size>
                                     <width>120</width>
                                     <height>35</height>
                                    </size>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QPushButton" name="Com_Open_Button">
                                   <property name="minimumSize">
                                    <size>
                                     <width>120</width>
                                     <height>35</height>
                                    </size>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <property name="text">
                                    <string>打开</string>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QPushButton" name="Com_Close_Button">
                                   <property name="minimumSize">
                                    <size>
                                     <width>120</width>
                                     <height>35</height>
                                    </size>
                                   </property>
                                   <property name="cursor">
                                    <cursorShape>PointingHandCursor</cursorShape>
                                   </property>
                                   <property name="text">
                                    <string>关闭</string>
                                   </property>
                                  </widget>
                                 </item>
                                </layout>
                               </item>
                              </layout>
                             </widget>
                            </item>
                            <item>
                             <widget class="QFrame" name="frame_11">
                              <property name="minimumSize">
                               <size>
                                <width>275</width>
                                <height>280</height>
                               </size>
                              </property>
                              <property name="frameShape">
                               <enum>QFrame::StyledPanel</enum>
                              </property>
                              <property name="frameShadow">
                               <enum>QFrame::Raised</enum>
                              </property>
                              <layout class="QVBoxLayout" name="verticalLayout_33">
                               <property name="leftMargin">
                                <number>0</number>
                               </property>
                               <property name="topMargin">
                                <number>5</number>
                               </property>
                               <property name="rightMargin">
                                <number>0</number>
                               </property>
                               <property name="bottomMargin">
                                <number>0</number>
                               </property>
                               <item>
                                <layout class="QVBoxLayout" name="verticalLayout_28">
                                 <item>
                                  <layout class="QHBoxLayout" name="horizontalLayout_14">
                                   <item>
                                    <layout class="QVBoxLayout" name="verticalLayout_18">
                                     <item>
                                      <widget class="QLabel" name="label_9">
                                       <property name="text">
                                        <string>升级</string>
                                       </property>
                                       <property name="alignment">
                                        <set>Qt::AlignCenter</set>
                                       </property>
                                      </widget>
                                     </item>
                                     <item>
                                      <widget class="QLabel" name="label_14">
                                       <property name="text">
                                        <string>速率</string>
                                       </property>
                                       <property name="alignment">
                                        <set>Qt::AlignCenter</set>
                                       </property>
                                      </widget>
                                     </item>
                                     <item>
                                      <widget class="QLabel" name="label_17">
                                       <property name="text">
                                        <string>文件路径</string>
                                       </property>
                                       <property name="alignment">
                                        <set>Qt::AlignCenter</set>
                                       </property>
                                      </widget>
                                     </item>
                                    </layout>
                                   </item>
                                   <item>
                                    <layout class="QVBoxLayout" name="verticalLayout_26">
                                     <item>
                                      <widget class="QPushButton" name="pushButton_2">
                                       <property name="minimumSize">
                                        <size>
                                         <width>120</width>
                                         <height>35</height>
                                        </size>
                                       </property>
                                       <property name="cursor">
                                        <cursorShape>PointingHandCursor</cursorShape>
                                       </property>
                                       <property name="text">
                                        <string>开始升级</string>
                                       </property>
                                      </widget>
                                     </item>
                                     <item>
                                      <widget class="QComboBox" name="comboBox">
                                       <property name="minimumSize">
                                        <size>
                                         <width>120</width>
                                         <height>35</height>
                                        </size>
                                       </property>
                                       <item>
                                        <property name="text">
                                         <string>128</string>
                                        </property>
                                       </item>
                                       <item>
                                        <property name="text">
                                         <string>1024</string>
                                        </property>
                                       </item>
                                      </widget>
                                     </item>
                                     <item>
                                      <widget class="QPushButton" name="Com_Open_file">
                                       <property name="minimumSize">
                                        <size>
                                         <width>120</width>
                                         <height>35</height>
                                        </size>
                                       </property>
                                       <property name="cursor">
                                        <cursorShape>PointingHandCursor</cursorShape>
                                       </property>
                                       <property name="text">
                                        <string>打开文件</string>
                                       </property>
                                      </widget>
                                     </item>
                                    </layout>
                                   </item>
                                  </layout>
                                 </item>
                                 <item>
                                  <layout class="QVBoxLayout" name="verticalLayout_27">
                                   <item>
                                    <widget class="QLabel" name="Com_isOpenOrNot_Label">
                                     <property name="minimumSize">
                                      <size>
                                       <width>0</width>
                                       <height>35</height>
                                      </size>
                                     </property>
                                     <property name="text">
                                      <string>串口待打开</string>
                                     </property>
                                     <property name="alignment">
                                      <set>Qt::AlignCenter</set>
                                     </property>
                                    </widget>
                                   </item>
                                   <item>
                                    <widget class="QLineEdit" name="Com_file_line">
                                     <property name="minimumSize">
                                      <size>
                                       <width>0</width>
                                       <height>35</height>
                                      </size>
                                     </property>
                                    </widget>
                                   </item>
                                   <item>
                                    <widget class="QProgressBar" name="progressBar">
                                     <property name="minimumSize">
                                      <size>
                                       <width>0</width>
                                       <height>35</height>
                                      </size>
                                     </property>
                                     <property name="layoutDirection">
                                      <enum>Qt::LeftToRight</enum>
                                     </property>
                                     <property name="value">
                                      <number>24</number>
                                     </property>
                                     <property name="orientation">
                                      <enum>Qt::Horizontal</enum>
                                     </property>
                                     <property name="textDirection">
                                      <enum>QProgressBar::TopToBottom</enum>
                                     </property>
                                    </widget>
                                   </item>
                                  </layout>
                                 </item>
                                </layout>
                               </item>
                              </layout>
                             </widget>
                            </item>
                           </layout>
                          </item>
                          <item>
                           <layout class="QVBoxLayout" name="verticalLayout_31">
                            <item>
                             <widget class="QFrame" name="frame_4">
                              <property name="minimumSize">
                               <size>
                                <width>610</width>
                                <height>106</height>
                               </size>
                              </property>
                              <property name="frameShape">
                               <enum>QFrame::StyledPanel</enum>
                              </property>
                              <property name="frameShadow">
                               <enum>QFrame::Raised</enum>
                              </property>
                              <layout class="QVBoxLayout" name="verticalLayout_29">
                               <property name="leftMargin">
                                <number>0</number>
                               </property>
                               <property name="topMargin">
                                <number>0</number>
                               </property>
                               <property name="rightMargin">
                                <number>0</number>
                               </property>
                               <property name="bottomMargin">
                                <number>0</number>
                               </property>
                               <item>
                                <widget class="QComboBox" name="Command_select">
                                 <property name="minimumSize">
                                  <size>
                                   <width>0</width>
                                   <height>35</height>
                                  </size>
                                 </property>
                                 <item>
                                  <property name="text">
                                   <string>motor</string>
                                  </property>
                                 </item>
                                 <item>
                                  <property name="text">
                                   <string>meas</string>
                                  </property>
                                 </item>
                                </widget>
                               </item>
                               <item>
                                <widget class="QLineEdit" name="lineEdit_3">
                                 <property name="minimumSize">
                                  <size>
                                   <width>0</width>
                                   <height>35</height>
                                  </size>
                                 </property>
                                 <property name="placeholderText">
                                  <string>命令参数</string>
                                 </property>
                                </widget>
                               </item>
                              </layout>
                             </widget>
                            </item>
                            <item>
                             <spacer name="verticalSpacer_2">
                              <property name="orientation">
                               <enum>Qt::Vertical</enum>
                              </property>
                              <property name="sizeHint" stdset="0">
                               <size>
                                <width>20</width>
                                <height>118</height>
                               </size>
                              </property>
                             </spacer>
                            </item>
                            <item>
                             <widget class="QFrame" name="frame_3">
                              <property name="minimumSize">
                               <size>
                                <width>630</width>
                                <height>160</height>
                               </size>
                              </property>
                              <property name="frameShape">
                               <enum>QFrame::StyledPanel</enum>
                              </property>
                              <property name="frameShadow">
                               <enum>QFrame::Raised</enum>
                              </property>
                              <layout class="QVBoxLayout" name="verticalLayout_32">
                               <property name="leftMargin">
                                <number>0</number>
                               </property>
                               <property name="topMargin">
                                <number>0</number>
                               </property>
                               <property name="rightMargin">
                                <number>0</number>
                               </property>
                               <property name="bottomMargin">
                                <number>0</number>
                               </property>
                               <item>
                                <layout class="QHBoxLayout" name="horizontalLayout_11">
                                 <item>
                                  <widget class="QLabel" name="label_7">
                                   <property name="minimumSize">
                                    <size>
                                     <width>120</width>
                                     <height>35</height>
                                    </size>
                                   </property>
                                   <property name="text">
                                    <string>发送端</string>
                                   </property>
                                   <property name="alignment">
                                    <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <spacer name="horizontalSpacer">
                                   <property name="orientation">
                                    <enum>Qt::Horizontal</enum>
                                   </property>
                                   <property name="sizeHint" stdset="0">
                                    <size>
                                     <width>208</width>
                                     <height>20</height>
                                    </size>
                                   </property>
                                  </spacer>
                                 </item>
                                 <item>
                                  <widget class="QCheckBox" name="hexSending_checkBox">
                                   <property name="sizePolicy">
                                    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                                     <horstretch>0</horstretch>
                                     <verstretch>0</verstretch>
                                    </sizepolicy>
                                   </property>
                                   <property name="text">
                                    <string>16进制</string>
                                   </property>
                                  </widget>
                                 </item>
                                 <item>
                                  <widget class="QPushButton" name="Send_Button">
                                   <property name="minimumSize">
                                    <size>
                                     <width>120</width>
                                     <height>35</height>
                                    </size>
                                   </property>
                                   <property name="text">
                                    <string>发送</string>
                                   </property>
                                  </widget>
                                 </item>
                                </layout>
                               </item>
                               <item>
                                <widget class="QTextEdit" name="textEdit_Send">
                                 <property name="sizePolicy">
                                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                                   <horstretch>0</horstretch>
                                   <verstretch>0</verstretch>
                                  </sizepolicy>
                                 </property>
                                 <property name="minimumSize">
                                  <size>
                                   <width>520</width>
                                   <height>100</height>
                                  </size>
                                 </property>
                                 <property name="styleSheet">
                                  <string notr="true">QTextEdit#textEdit_Send{
	border-radius: 20px;
	border: 1px solid black;
	background-color: rgb(241, 241, 241);
}</string>
                                 </property>
                                </widget>
                               </item>
                              </layout>
                             </widget>
                            </item>
                           </layout>
                          </item>
                         </layout>
                        </widget>
                       </item>
                      </layout>
                     </widget>
                     <widget class="QWidget" name="Real_time_charts">
                      <layout class="QVBoxLayout" name="verticalLayout_36">
                       <property name="leftMargin">
                        <number>0</number>
                       </property>
                       <property name="topMargin">
                        <number>0</number>
                       </property>
                       <property name="rightMargin">
                        <number>0</number>
                       </property>
                       <property name="bottomMargin">
                        <number>0</number>
                       </property>
                       <item>
                        <widget class="QFrame" name="frame_13">
                         <property name="frameShape">
                          <enum>QFrame::StyledPanel</enum>
                         </property>
                         <property name="frameShadow">
                          <enum>QFrame::Raised</enum>
                         </property>
                         <layout class="QVBoxLayout" name="verticalLayout_35">
                          <property name="leftMargin">
                           <number>0</number>
                          </property>
                          <property name="topMargin">
                           <number>0</number>
                          </property>
                          <property name="rightMargin">
                           <number>0</number>
                          </property>
                          <property name="bottomMargin">
                           <number>0</number>
                          </property>
                          <item>
                           <layout class="QVBoxLayout" name="verticalLayout_25">
                            <item>
                             <layout class="QHBoxLayout" name="horizontalLayout_28">
                              <item>
                               <widget class="QLineEdit" name="Chart_line">
                                <property name="minimumSize">
                                 <size>
                                  <width>1020</width>
                                  <height>35</height>
                                 </size>
                                </property>
                               </widget>
                              </item>
                              <item>
                               <widget class="QPushButton" name="Chart_btn">
                                <property name="minimumSize">
                                 <size>
                                  <width>110</width>
                                  <height>35</height>
                                 </size>
                                </property>
                                <property name="cursor">
                                 <cursorShape>PointingHandCursor</cursorShape>
                                </property>
                                <property name="text">
                                 <string>发送</string>
                                </property>
                               </widget>
                              </item>
                             </layout>
                            </item>
                            <item>
                             <widget class="QWidget" name="Chart_widget" native="true">
                              <property name="minimumSize">
                               <size>
                                <width>1140</width>
                                <height>650</height>
                               </size>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">background-color: rgb(255, 255, 255);</string>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </item>
                         </layout>
                        </widget>
                       </item>
                      </layout>
                     </widget>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QFrame" name="extraRightBox">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>0</width>
                    <height>********</height>
                   </size>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_7">
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <widget class="QFrame" name="contentSettings">
                     <property name="frameShape">
                      <enum>QFrame::NoFrame</enum>
                     </property>
                     <property name="frameShadow">
                      <enum>QFrame::Raised</enum>
                     </property>
                     <layout class="QVBoxLayout" name="verticalLayout_13">
                      <property name="spacing">
                       <number>0</number>
                      </property>
                      <property name="leftMargin">
                       <number>0</number>
                      </property>
                      <property name="topMargin">
                       <number>0</number>
                      </property>
                      <property name="rightMargin">
                       <number>0</number>
                      </property>
                      <property name="bottomMargin">
                       <number>0</number>
                      </property>
                      <item alignment="Qt::AlignTop">
                       <widget class="QFrame" name="topMenus">
                        <property name="frameShape">
                         <enum>QFrame::NoFrame</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_14">
                         <property name="spacing">
                          <number>0</number>
                         </property>
                         <property name="leftMargin">
                          <number>0</number>
                         </property>
                         <property name="topMargin">
                          <number>0</number>
                         </property>
                         <property name="rightMargin">
                          <number>0</number>
                         </property>
                         <property name="bottomMargin">
                          <number>0</number>
                         </property>
                         <item>
                          <widget class="QPushButton" name="btn_message">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>45</height>
                            </size>
                           </property>
                           <property name="font">
                            <font>
                             <family>微软雅黑</family>
                             <pointsize>11</pointsize>
                             <weight>50</weight>
                             <italic>false</italic>
                             <bold>false</bold>
                            </font>
                           </property>
                           <property name="cursor">
                            <cursorShape>PointingHandCursor</cursorShape>
                           </property>
                           <property name="layoutDirection">
                            <enum>Qt::LeftToRight</enum>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">background-image: url(:/icons/images/icons/cil-envelope-open.png);</string>
                           </property>
                           <property name="text">
                            <string>Message</string>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QPushButton" name="btn_print">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>45</height>
                            </size>
                           </property>
                           <property name="font">
                            <font>
                             <family>微软雅黑</family>
                             <pointsize>11</pointsize>
                             <weight>50</weight>
                             <italic>false</italic>
                             <bold>false</bold>
                            </font>
                           </property>
                           <property name="cursor">
                            <cursorShape>PointingHandCursor</cursorShape>
                           </property>
                           <property name="layoutDirection">
                            <enum>Qt::LeftToRight</enum>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">background-image: url(:/icons/images/icons/cil-print.png);</string>
                           </property>
                           <property name="text">
                            <string>Print</string>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QPushButton" name="btn_logout">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>45</height>
                            </size>
                           </property>
                           <property name="font">
                            <font>
                             <family>微软雅黑</family>
                             <pointsize>11</pointsize>
                             <weight>50</weight>
                             <italic>false</italic>
                             <bold>false</bold>
                            </font>
                           </property>
                           <property name="cursor">
                            <cursorShape>PointingHandCursor</cursorShape>
                           </property>
                           <property name="layoutDirection">
                            <enum>Qt::LeftToRight</enum>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">background-image: url(:/icons/images/icons/cil-account-logout.png);</string>
                           </property>
                           <property name="text">
                            <string>Logout</string>
                           </property>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="bottomBar">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>22</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>********</width>
                 <height>22</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_5">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="creditsLabel">
                  <property name="maximumSize">
                   <size>
                    <width>********</width>
                    <height>16</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>微软雅黑</family>
                    <pointsize>-1</pointsize>
                    <weight>50</weight>
                    <italic>false</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>By: Qing An</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="version">
                  <property name="text">
                   <string>v1.0.9</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QFrame" name="frame_size_grip">
                  <property name="minimumSize">
                   <size>
                    <width>20</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>********</height>
                   </size>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>
